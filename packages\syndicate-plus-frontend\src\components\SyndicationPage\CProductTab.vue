<template>
  <div class="c-product-tab">
    <div class="search-panel flex">
      <q-checkbox
        :model-value="allSelectedStatus"
        :class="checkBoxClass"
        :disable="loading"
        :label="$t('syndicate_plus.common.select_all')"
        v-bind="$inri.checkbox"
        @click="toggleAllItems"
      />
      <c-inri-search
        v-model="searchValue"
        class="c-inri-custom-search"
        :class="customSearchClass"
        dense
        :placeholder="$t('syndicate_plus.common.filter.search')"
        @search="search"
        @keydown.enter.prevent="search"
        @update:model-value="onSearchValueChanged"
        @clear="clearSearch"
      />
      <c-selection-counter
        :selected-items="numberOfSelectedProducts"
        :total-items="totalItems"
        :total-items-text="$t(`syndicate_plus.common.group_by.${getFilter().groupBy.id}`)"
      />
    </div>
    <c-section v-if="products?.length || loading" id="products-section" class="pt-4 pr-0">
      <q-table
        :pagination="{
          page: 1,
          rowsPerPage: 0,
        }"
        dense
        hide-bottom
        :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
        flat
        :grid="isGridView"
        :card-container-class="isGridView ? 'grid card-container' : ''"
        :columns="columns"
        class="product-table hide-checkboxes sticky-table-header"
        :rows-per-page-options="[0]"
        :rows="products"
        row-key="id"
        separator="cell"
        :loading="loading"
        virtual-scroll
        :virtual-scroll-item-size="31"
        :virtual-scroll-sticky-size-start="31"
        :selected="selectedProducts"
        @virtual-scroll="onScroll"
        @scroll="onGridScroll"
        @row-click="(event, row) => onItemClick(event as PointerEvent, row)"
      >
        <template #body-cell-updated="props">
          <q-td :props="props">
            <c-small-square
              :color="props.row['isUpdatedSinceLastSyndication'] ? SquareColor.GREEN : SquareColor.UNKNOWN"
            >
              <q-tooltip v-if="props.row['isUpdatedSinceLastSyndication']">
                {{ $t('syndicate_plus.syndication.updated') }}
              </q-tooltip>
            </c-small-square>
          </q-td>
        </template>
        <template #body-cell-sku="props">
          <q-td :props="props">
            {{ props.row.sku.join(', ') }}
          </q-td>
        </template>
        <template #body-cell-type="props">
          <q-td :props="props">
            {{ $t(`syndicate_plus.syndication.product_type.${getFilter().groupBy.id}`) }}
          </q-td>
        </template>
        <template #body-cell-aplusTemplate="props">
          <q-td :props="props">
            {{ getAplusFieldValue(props.row, 'templateName') }}
          </q-td>
        </template>
        <template #body-cell-aplusStatus="props">
          <q-td :props="props">
            {{ getAplusFieldValue(props.row, 'aplusStatus') }}
          </q-td>
        </template>
        <template #loading>
          <div class="row justify-center q-my-md">
            <c-spinner data-testid="products-spinner" color="primary" size="40" />
          </div>
        </template>
        <template #item="props">
          <c-product-card
            :key="props.row.id"
            :product="props.row"
            :group-by="getFilter().groupBy.id"
            :is-selected="isSelected(props.row)"
            @click="onItemClick($event, props.row)"
          />
        </template>
      </q-table>
    </c-section>
    <c-section v-else>
      <c-no-data
        src="nothing-to-see"
        image-height="195px"
        :title="$t('syndicate_plus.no_data.products.title')"
        :text="
          isFeatureEnabled('syndicate-advance')
            ? $t('syndicate_plus.no_data.products.syndicate_advance_message')
            : $t('syndicate_plus.no_data.products.syndicate_plus_message')
        "
      />
    </c-section>
  </div>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { Product } from '@customTypes';
import { Exception } from '@inriver/inri/src/plugins/errors/Exception';
import { onBeforeMount, watchEffect, ref, watch, computed, nextTick } from 'vue';
import { useProductsStore, useAppInsightsStore } from '@stores';
import { useRoute } from 'vue-router';
import { CSmallSquare, CProductCard, CInriSearch, CNoData } from '@components';
import { CSelectionCounter } from '@components/Shared';
import { SquareColor, SearchState, PageName, ProductGrouping } from '@enums';
import { useLazySelectable } from '@composables';
import { productsTableColumns, amazonExtraColumns } from '@const';
import { AmazonService } from '@services';
import isFeatureEnabled from '@utils/isFeatureEnabled';

const { setScreenName } = useAppInsightsStore();

const componentProps = defineProps({
  isGridView: {
    type: Boolean,
    required: true,
  },
  tradingPartnerName: {
    type: String,
    required: true,
  },
  hasSyndicateAplusAccess: {
    type: Boolean,
    default: false,
  },
});

// Refs
const products = ref([] as Product[]);
const searchValue = ref('');
const scrollKey = ref(0);

// Variables
const { loading, lastPage, totalItems, numberOfSelectedProducts, selectedProducts } = storeToRefs(useProductsStore());
const {
  fetchProducts,
  getProducts,
  getFilter,
  performSearch,
  updateSearchState,
  getSearchState,
  toggleSelectAll,
  getCurrentSearchValue,
  markProductsAsInitialized,
  getAreProductsIntialized,
  resetSelectedProducts,
} = useProductsStore();
const { allSelectedStatus, isSelected, toggleAllItems, onItemClick } = useLazySelectable<Product>(
  products,
  selectedProducts,
  numberOfSelectedProducts,
  totalItems,
  toggleSelectAll
);
const route = useRoute();
const { subCatalogId, destinationId } = route.params;

onBeforeMount(async () => {
  setScreenName(PageName.PRODUCTS);

  try {
    await fetchProducts(subCatalogId, destinationId);
    if (getAreProductsIntialized()) {
      searchValue.value = getCurrentSearchValue();
    }

    markProductsAsInitialized();
  } catch (error) {
    throw new Exception('Could not load data', error);
  }
});

watchEffect(() => {
  products.value = getProducts();
});

watch(
  () => getFilter(),
  async () => {
    await nextTick();
    scrollKey.value += 1;
    resetSelectedProducts();
  }
);

// Computed fields
const checkBoxClass = computed(() => {
  if (allSelectedStatus.value === undefined) {
    return '';
  }
  return allSelectedStatus.value ? 'dark' : 'light';
});

const customSearchClass = computed(() => {
  const currentSearchState = getSearchState();
  switch (currentSearchState) {
    case SearchState.SearchNotStarted:
      return '';
    case SearchState.SearchPending:
      return 'search-pending';
    case SearchState.SearchFinished:
      return 'search-finish';
    default:
      return '';
  }
});

const columns = computed(() => {
  let filteredColumns = productsTableColumns;

  switch (getFilter().groupBy.id) {
    case ProductGrouping.CUSTOM: {
      filteredColumns = filteredColumns.filter((obj) => {
        return obj.name != 'sku' && obj.name != 'upc';
      });
    }
    case ProductGrouping.SKU: {
      filteredColumns = filteredColumns.filter((obj) => {
        return obj.name != 'upc';
      });
    }
  }

  if (
    AmazonService.isTradingPartnerAmazon(componentProps.tradingPartnerName) &&
    componentProps.hasSyndicateAplusAccess
  ) {
    return [...filteredColumns, ...amazonExtraColumns];
  } else {
    return filteredColumns;
  }
});

// Functions
async function onScroll(details) {
  const { to, ref } = details;
  const isPageBottom = to === products.value.length - 1;
  if (!loading.value && !lastPage.value && isPageBottom) {
    await fetchProducts(subCatalogId, destinationId);
    nextTick(() => {
      ref.refresh();
    });
  }
}

async function onGridScroll(event) {
  const { scrollHeight, clientHeight, scrollTop } = event.target;
  const isPageBottom = scrollHeight - clientHeight - scrollTop < 331;
  if (!loading.value && !lastPage.value && isPageBottom) {
    await fetchProducts(subCatalogId, destinationId);
  }
}

function clearSearch() {
  searchValue.value = '';
}

async function search() {
  if (loading.value) {
    await delay(500);
    await search();
    return;
  }
  await performSearch(searchValue.value, subCatalogId, destinationId);
}

async function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

function onSearchValueChanged(): void {
  if (!searchValue.value) {
    updateSearchState(SearchState.SearchNotStarted);
    search();
    return;
  }
  updateSearchState(SearchState.SearchPending);
}

const getAplusFieldValue = (row, fieldName: string): string => {
  const firstAplusTemplate = row.aplusTemplateList?.length && row.aplusTemplateList[0];

  return firstAplusTemplate[fieldName]?.toLowerCase();
};
</script>

<style lang="scss" scoped>
$product-table-offset: 236px;
$card-width: 193px;

.c-product-tab {
  .search-panel {
    background-color: var(--color-grey-lighter);
    padding: 0;
    margin-top: 2px;
    flex-direction: row;
    align-items: center;
    height: 40px;
    max-height: 40px;

    .c-inri-checkbox {
      padding-left: 10px;
    }

    .c-inri-input {
      padding-left: 10px;
      min-width: 50%;
      border-right: 5px solid var(--surface-color);
      border-left: 5px solid var(--surface-color);
    }

    .q-checkbox {
      min-width: 25%;
    }

    :deep(.q-checkbox) {
      &.dark .q-checkbox__bg {
        background-color: var(--color-grey-darkest);
      }

      &.light .q-checkbox__bg {
        background-color: var(--surface-color);
      }
    }
  }

  .product-table {
    overflow: auto;
    max-height: calc(100vh - $product-table-offset);
  }

  :deep(.grid.card-container) {
    gap: 24px;
    grid-template-columns: repeat(auto-fill, minmax($card-width, $card-width - 20px));
  }

  :deep(.q-table--dense .q-table) {
    td:first-child {
      width: 30px;
    }
  }
}

:deep(.c-inri-input.c-inri-custom-search) {
  &.q-field--outlined .q-field__control {
    padding: 0px;
    border-radius: 0px;
    background: var(--color-grey-lighter);

    &::after {
      border: 0 !important;
    }
  }

  &.c-inri-input--dense.q-field--outlined .q-field__control::before {
    border: 0 !important;
  }

  .q-field__inner .q-field__control {
    padding: 0px;
    background: var(--color-grey-lighter);
    border-radius: 0px;

    .q-field__append span {
      width: 40px;
      height: 40px;
      border-left: 1px solid var(--surface-color);

      svg {
        height: 50%;
        width: 50%;
      }
    }
  }

  &.search-pending {
    .q-field__append span {
      color: var(--color-grey-darkest);
    }

    .icon-close {
      color: var(--color-grey-dark);
    }
  }

  &.search-finish {
    .icon-close {
      color: var(--color-grey-darkest);
    }
  }
}
</style>

import { describe, expect, it, beforeEach, vi } from 'vitest';
import { ref } from 'vue';
import useSelection from './useSelection';

interface MockItem {
  id: number;
  name: string;
}

describe('useSelection', () => {
  // Test data
  const mockItems: MockItem[] = [
    { id: 1, name: 'Item 1' },
    { id: 2, name: 'Item 2' },
    { id: 3, name: 'Item 3' },
    { id: 4, name: 'Item 4' },
    { id: 5, name: 'Item 5' },
  ];

  beforeEach(() => {
    // Reset any global state before each test
    vi.clearAllMocks();
  });

  describe('Single Selection Mode (default)', () => {
    it('should initialize with empty selection', () => {
      const { selectedItems } = useSelection<MockItem>();

      expect(selectedItems.value).toEqual([]);
    });

    it('should select a single item when onRowClick is called', () => {
      const { selectedItems, onRowClick } = useSelection<MockItem>();

      onRowClick(null, mockItems[0]);

      expect(selectedItems.value).toEqual([mockItems[0]]);
    });

    it('should deselect item when clicking on already selected item', () => {
      const { selectedItems, onRowClick } = useSelection<MockItem>();

      // Select item
      onRowClick(null, mockItems[0]);
      expect(selectedItems.value).toEqual([mockItems[0]]);

      // Deselect item
      onRowClick(null, mockItems[0]);
      expect(selectedItems.value).toEqual([]);
    });

    it('should replace selection when clicking on different item', () => {
      const { selectedItems, onRowClick } = useSelection<MockItem>();

      onRowClick(null, mockItems[0]);
      expect(selectedItems.value).toEqual([mockItems[0]]);

      onRowClick(null, mockItems[1]);
      expect(selectedItems.value).toEqual([mockItems[1]]);
    });

    it('should select item and call callback on double click', () => {
      const callback = vi.fn();
      const { selectedItems, onRowDoubleClick } = useSelection<MockItem>();

      onRowDoubleClick(null, mockItems[0], callback);

      expect(selectedItems.value).toEqual([mockItems[0]]);
      expect(callback).toHaveBeenCalledTimes(1);
    });

    it('should work without callback on double click', () => {
      const { selectedItems, onRowDoubleClick } = useSelection<MockItem>();

      expect(() => {
        onRowDoubleClick(null, mockItems[0]);
      }).not.toThrow();

      expect(selectedItems.value).toEqual([mockItems[0]]);
    });

    it('should check if item is selected correctly', () => {
      const { onRowClick, isSelected } = useSelection<MockItem>();

      expect(isSelected(mockItems[0])).toBe(false);

      onRowClick(null, mockItems[0]);
      expect(isSelected(mockItems[0])).toBe(true);
      expect(isSelected(mockItems[1])).toBe(false);
    });

    it('should clear selected items', () => {
      const { selectedItems, onRowClick, clearSelectedItems } = useSelection<MockItem>();

      onRowClick(null, mockItems[0]);
      expect(selectedItems.value).toEqual([mockItems[0]]);

      clearSelectedItems();
      expect(selectedItems.value).toEqual([]);
    });

    it('should provide selectedRows alias for compatibility', () => {
      const { selectedItems, selectedRows, onRowClick } = useSelection<MockItem>();

      onRowClick(null, mockItems[0]);

      expect(selectedRows.value).toBe(selectedItems.value);
      expect(selectedRows.value).toEqual([mockItems[0]]);
    });
  });

  describe('Multi Selection Mode', () => {
    it('should initialize with empty selection in multi mode', () => {
      const allItems = ref(mockItems);
      const { selectedItems } = useSelection<MockItem>(allItems, { multiple: true });

      expect(selectedItems.value).toEqual([]);
    });

    it('should warn when allItems is not provided in multi mode', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {
        // Intentionally empty
      });
      const { onItemClick } = useSelection<MockItem>(undefined, { multiple: true });

      const event = new PointerEvent('click', { ctrlKey: false, shiftKey: false, metaKey: false });
      onItemClick(event, mockItems[0]);

      expect(consoleSpy).toHaveBeenCalledWith('allItems reference is required for multi-selection');
      consoleSpy.mockRestore();
    });

    it('should select single item on simple click', () => {
      const allItems = ref(mockItems);
      const { selectedItems, onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      const event = new PointerEvent('click', { ctrlKey: false, shiftKey: false, metaKey: false });
      onItemClick(event, mockItems[0]);

      expect(selectedItems.value).toEqual([mockItems[0]]);
    });

    it('should toggle item selection with Ctrl+click', () => {
      const allItems = ref(mockItems);
      const { selectedItems, onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      // Select first item
      const ctrlEvent = new PointerEvent('click', { ctrlKey: true, shiftKey: false, metaKey: false });
      onItemClick(ctrlEvent, mockItems[0]);
      expect(selectedItems.value).toEqual([mockItems[0]]);

      // Add second item with Ctrl+click
      onItemClick(ctrlEvent, mockItems[1]);
      expect(selectedItems.value).toEqual([mockItems[0], mockItems[1]]);

      // Remove first item with Ctrl+click
      onItemClick(ctrlEvent, mockItems[0]);
      expect(selectedItems.value).toEqual([mockItems[1]]);
    });

    it('should toggle item selection with Meta+click (Mac)', () => {
      const allItems = ref(mockItems);
      const { selectedItems, onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      const metaEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: false, metaKey: true });
      onItemClick(metaEvent, mockItems[0]);
      expect(selectedItems.value).toEqual([mockItems[0]]);

      onItemClick(metaEvent, mockItems[1]);
      expect(selectedItems.value).toEqual([mockItems[0], mockItems[1]]);
    });

    it('should select range with Shift+click when no items selected', () => {
      const allItems = ref(mockItems);
      const { selectedItems, onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      const shiftEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: true, metaKey: false });
      onItemClick(shiftEvent, mockItems[2]);

      expect(selectedItems.value).toEqual([mockItems[2]]);
    });

    it('should select range with Shift+click when items already selected', () => {
      const allItems = ref(mockItems);
      const { selectedItems, onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      // First select an item
      const normalEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: false, metaKey: false });
      onItemClick(normalEvent, mockItems[1]);
      expect(selectedItems.value).toEqual([mockItems[1]]);

      // Then shift+click on another item to select range
      const shiftEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: true, metaKey: false });
      onItemClick(shiftEvent, mockItems[3]);

      expect(selectedItems.value).toEqual([mockItems[1], mockItems[2], mockItems[3]]);
    });

    it('should select range in reverse order with Shift+click', () => {
      const allItems = ref(mockItems);
      const { selectedItems, onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      // First select an item at higher index
      const normalEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: false, metaKey: false });
      onItemClick(normalEvent, mockItems[3]);
      expect(selectedItems.value).toEqual([mockItems[3]]);

      // Then shift+click on item at lower index
      const shiftEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: true, metaKey: false });
      onItemClick(shiftEvent, mockItems[1]);

      expect(selectedItems.value).toEqual([mockItems[3], mockItems[1], mockItems[2]]);
    });

    it('should not add duplicate items when shift selecting overlapping range', () => {
      const allItems = ref(mockItems);
      const { selectedItems, onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      // First select items 1 and 2
      const normalEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: false, metaKey: false });
      onItemClick(normalEvent, mockItems[1]);

      const ctrlEvent = new PointerEvent('click', { ctrlKey: true, shiftKey: false, metaKey: false });
      onItemClick(ctrlEvent, mockItems[2]);

      expect(selectedItems.value).toEqual([mockItems[1], mockItems[2]]);

      // Then shift+click to select range that includes already selected items
      const shiftEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: true, metaKey: false });
      onItemClick(shiftEvent, mockItems[4]);

      expect(selectedItems.value).toEqual([mockItems[1], mockItems[2], mockItems[3], mockItems[4]]);
    });

    it('should unselect all when shift+click on already selected item', () => {
      const allItems = ref(mockItems);
      const { selectedItems, onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      // First select an item
      const normalEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: false, metaKey: false });
      onItemClick(normalEvent, mockItems[1]);
      expect(selectedItems.value).toEqual([mockItems[1]]);

      // Then shift+click on the same item
      const shiftEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: true, metaKey: false });
      onItemClick(shiftEvent, mockItems[1]);

      expect(selectedItems.value).toEqual([]);
    });

    it('should replace selection when clicking without modifiers on new item', () => {
      const allItems = ref(mockItems);
      const { selectedItems, onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      // First select multiple items
      const ctrlEvent = new PointerEvent('click', { ctrlKey: true, shiftKey: false, metaKey: false });
      onItemClick(ctrlEvent, mockItems[0]);
      onItemClick(ctrlEvent, mockItems[1]);
      expect(selectedItems.value).toEqual([mockItems[0], mockItems[1]]);

      // Then click on new item without modifiers
      const normalEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: false, metaKey: false });
      onItemClick(normalEvent, mockItems[2]);

      expect(selectedItems.value).toEqual([mockItems[2]]);
    });

    it('should fallback to single selection behavior when multiple is false', () => {
      const allItems = ref(mockItems);
      const { selectedItems, onItemClick } = useSelection<MockItem>(allItems, { multiple: false });

      const normalEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: false, metaKey: false });
      onItemClick(normalEvent, mockItems[0]);
      expect(selectedItems.value).toEqual([mockItems[0]]);

      // Click on second item should replace selection
      onItemClick(normalEvent, mockItems[1]);
      expect(selectedItems.value).toEqual([mockItems[1]]);

      // Click on same item should deselect
      onItemClick(normalEvent, mockItems[1]);
      expect(selectedItems.value).toEqual([]);
    });
  });

  describe('Common Methods', () => {
    it('should clear selected items using clearSelectedItems', () => {
      const allItems = ref(mockItems);
      const { selectedItems, onItemClick, clearSelectedItems } = useSelection<MockItem>(allItems, { multiple: true });

      // Select multiple items
      const ctrlEvent = new PointerEvent('click', { ctrlKey: true, shiftKey: false, metaKey: false });
      onItemClick(ctrlEvent, mockItems[0]);
      onItemClick(ctrlEvent, mockItems[1]);
      expect(selectedItems.value).toEqual([mockItems[0], mockItems[1]]);

      clearSelectedItems();
      expect(selectedItems.value).toEqual([]);
    });

    it('should check if item is selected correctly in multi mode', () => {
      const allItems = ref(mockItems);
      const { onItemClick, isSelected } = useSelection<MockItem>(allItems, { multiple: true });

      expect(isSelected(mockItems[0])).toBe(false);

      const ctrlEvent = new PointerEvent('click', { ctrlKey: true, shiftKey: false, metaKey: false });
      onItemClick(ctrlEvent, mockItems[0]);

      expect(isSelected(mockItems[0])).toBe(true);
      expect(isSelected(mockItems[1])).toBe(false);
    });

    it('should remove text selection when onItemClick is called', () => {
      const allItems = ref(mockItems);
      const { onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      // Mock document.getSelection
      const mockRemoveAllRanges = vi.fn();
      const mockGetSelection = vi.fn().mockReturnValue({
        removeAllRanges: mockRemoveAllRanges,
      });

      // Mock the global document object
      Object.defineProperty(global, 'document', {
        value: {
          getSelection: mockGetSelection,
        },
        writable: true,
      });

      const normalEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: false, metaKey: false });
      onItemClick(normalEvent, mockItems[0]);

      expect(mockGetSelection).toHaveBeenCalled();
      expect(mockRemoveAllRanges).toHaveBeenCalled();
    });

    it('should handle missing document.getSelection gracefully', () => {
      const allItems = ref(mockItems);
      const { onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      // Mock document without getSelection
      Object.defineProperty(global, 'document', {
        value: null,
        writable: true,
      });

      const normalEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: false, metaKey: false });

      expect(() => {
        onItemClick(normalEvent, mockItems[0]);
      }).not.toThrow();
    });
  });

  describe('Internal Methods', () => {
    it('should add items correctly', () => {
      const allItems = ref(mockItems);
      const { selectedItems, onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      const ctrlEvent = new PointerEvent('click', { ctrlKey: true, shiftKey: false, metaKey: false });
      onItemClick(ctrlEvent, mockItems[0]);
      onItemClick(ctrlEvent, mockItems[2]);

      expect(selectedItems.value).toEqual([mockItems[0], mockItems[2]]);
    });

    it('should find existing items using JSON comparison', () => {
      const allItems = ref(mockItems);
      const { selectedItems, onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      // Create a copy of an item (different reference but same content)
      const itemCopy = { ...mockItems[0] };

      const ctrlEvent = new PointerEvent('click', { ctrlKey: true, shiftKey: false, metaKey: false });
      onItemClick(ctrlEvent, mockItems[0]);
      expect(selectedItems.value).toEqual([mockItems[0]]);

      // Click on the copy should remove the original (since JSON.stringify matches)
      onItemClick(ctrlEvent, itemCopy);
      expect(selectedItems.value).toEqual([]);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty allItems array', () => {
      const allItems = ref<MockItem[]>([]);
      const { selectedItems, onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      const normalEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: false, metaKey: false });

      expect(() => {
        onItemClick(normalEvent, mockItems[0]);
      }).not.toThrow();

      expect(selectedItems.value).toEqual([mockItems[0]]);
    });

    it('should handle items not in allItems array for shift selection', () => {
      const allItems = ref([mockItems[0], mockItems[2], mockItems[4]]); // Missing items 1 and 3
      const { selectedItems, onItemClick } = useSelection<MockItem>(allItems, { multiple: true });

      const normalEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: false, metaKey: false });
      onItemClick(normalEvent, mockItems[0]);

      const shiftEvent = new PointerEvent('click', { ctrlKey: false, shiftKey: true, metaKey: false });
      onItemClick(shiftEvent, mockItems[1]); // Item not in allItems

      // When item is not found, indexOf returns -1, which causes undefined to be added
      // This tests the actual behavior of the implementation
      expect(selectedItems.value).toEqual([mockItems[0], undefined]);
    });

    it('should handle null/undefined items gracefully', () => {
      const { onRowClick, isSelected } = useSelection<MockItem | null>();

      expect(() => {
        onRowClick(null, null);
        onRowClick(null, undefined as any);
        isSelected(null);
        isSelected(undefined as any);
      }).not.toThrow();
    });
  });
});

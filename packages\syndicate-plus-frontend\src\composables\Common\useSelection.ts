import { ref, Ref } from 'vue';

export interface SelectionOptions {
  /**
   * Whether to allow multiple item selection
   * @default false
   */
  multiple?: boolean;
}

/**
 * A unified selection composable that can handle both single and multi-selection patterns
 * @param allItems - The full list of items when using multiple selection
 * @param options - Selection options
 */
export default function useSelection<T>(allItems?: Ref<T[]>, options: SelectionOptions = { multiple: false }) {
  // Refs
  const selectedItems = ref<T[]>([]) as Ref<T[]>;

  // Functions for single selection
  const onRowClick = (_, row: T): void => {
    const newRowValue = selectedItems.value?.includes(row) ? null : row;
    selectedItems.value = newRowValue ? [newRowValue] : [];
  };

  const onRowDoubleClick = (_, row: T, callback?: Function): void => {
    selectedItems.value = [row];
    callback && callback();
  };

  // Functions for multi selection
  const onItemClick = (event: PointerEvent, item: T): void => {
    if (!options.multiple) {
      // Fallback to single selection behavior
      const newValue = selectedItems.value?.includes(item) ? null : item;
      selectedItems.value = newValue ? [newValue] : [];
      return;
    }

    if (!allItems) {
      console.warn('allItems reference is required for multi-selection');
      return;
    }

    const existingItemIndex: number = selectedItems.value?.findIndex((i) => JSON.stringify(i) === JSON.stringify(item));
    removeTextSelection();

    if (event.ctrlKey || event.metaKey) {
      toggleItem(item, existingItemIndex);
      return;
    }

    if (!event.shiftKey) {
      unselectAll();
      toggleItem(item, existingItemIndex);
      return;
    }

    if (event.shiftKey && !selectedItems.value?.length && !isSelected(item)) {
      addSelectedItem(item);
      return;
    }

    if (event.shiftKey && selectedItems.value?.length && !isSelected(item)) {
      const currentCardIndex = allItems.value.indexOf(item);
      const lastSelectedItem = selectedItems.value[selectedItems.value.length - 1];
      const lastSelectedCardIndex = allItems.value.indexOf(lastSelectedItem);

      const startIndex = lastSelectedCardIndex < currentCardIndex ? lastSelectedCardIndex : currentCardIndex;
      const stopIndex = lastSelectedCardIndex < currentCardIndex ? currentCardIndex : lastSelectedCardIndex;

      for (let i = startIndex; i <= stopIndex; i++) {
        if (selectedItems.value.indexOf(allItems.value[i]) < 0) {
          addSelectedItem(allItems.value[i]);
        }
      }
      return;
    } else {
      unselectAll();
    }
  };

  const isSelected = (item: T): boolean => selectedItems.value.includes(item);

  const clearSelectedItems = () => {
    selectedItems.value = [];
  };

  const toggleItem = (item: T, existingItemIndex: number): void => {
    if (existingItemIndex >= 0) {
      selectedItems.value.splice(existingItemIndex, 1);
    } else {
      addSelectedItem(item);
    }
  };

  const addSelectedItem = (item: T): void => {
    selectedItems.value.push(item);
  };

  const unselectAll = (): void => {
    selectedItems.value = [];
  };

  const removeTextSelection = () => document?.getSelection()?.removeAllRanges();

  return {
    // Common properties
    selectedItems,
    clearSelectedItems,
    isSelected,

    // Single selection specific
    selectedRows: selectedItems, // Alias for compatibility with useSingleRowSelect
    onRowClick,
    onRowDoubleClick,

    // Multi selection specific
    onItemClick,
  };
}

import { describe, it, expect, beforeEach } from 'vitest';
import { ref, nextTick, type Ref } from 'vue';
import { useApiFailureResponse } from './useApiFailureResponse';
import type { DataSubmissionEntity } from '@core/interfaces';

describe('useApiFailureResponse', () => {
  let entityRef: Ref<DataSubmissionEntity | null>;

  beforeEach(() => {
    entityRef = ref<DataSubmissionEntity | null>(null);
  });

  describe('parseApiFailureResponse', () => {
    it('should parse valid JSON API failure response', () => {
      const { parseApiFailureResponse } = useApiFailureResponse(entityRef);

      const validJson = JSON.stringify({
        Field1: [{ Level: 'error', Message: 'Field1 is required', MessageCode: 'ERR001' }],
        Field2: [{ Level: 'warning', Message: 'Field2 is optional', MessageCode: 'WARN001' }],
      });

      const result = parseApiFailureResponse(validJson);

      expect(result).toEqual({
        Field1: [{ Level: 'error', Message: 'Field1 is required', MessageCode: 'ERR001' }],
        Field2: [{ Level: 'warning', Message: 'Field2 is optional', MessageCode: 'WARN001' }],
      });
    });

    it('should handle invalid JSON by creating error message structure', () => {
      const { parseApiFailureResponse } = useApiFailureResponse(entityRef);

      const invalidJson = 'This is not valid JSON';
      const result = parseApiFailureResponse(invalidJson);

      expect(result).toEqual({
        'Error Message': [
          {
            Level: 'error',
            Message: 'This is not valid JSON',
            MessageCode: 'n/a',
          },
        ],
      });
    });
  });

  describe('parsedResponse reactivity', () => {
    it('should return null when entity is null', () => {
      const { parsedResponse } = useApiFailureResponse(entityRef);

      expect(parsedResponse.value).toBeNull();
    });

    it('should parse apiFailureResponse when entity is updated', async () => {
      const { parsedResponse } = useApiFailureResponse(entityRef);

      const apiResponse = JSON.stringify({
        ValidationErrors: [{ Level: 'error', Message: 'Invalid data', MessageCode: 'VAL001' }],
      });

      entityRef.value = {
        id: 1,
        apiFailureResponse: apiResponse,
      } as DataSubmissionEntity;

      // Wait for reactivity to update
      await nextTick();

      expect(parsedResponse.value).toEqual({
        ValidationErrors: [{ Level: 'error', Message: 'Invalid data', MessageCode: 'VAL001' }],
      });
    });

    it('should handle malformed JSON in apiFailureResponse', async () => {
      const { parsedResponse } = useApiFailureResponse(entityRef);

      entityRef.value = {
        id: 1,
        apiFailureResponse: 'Invalid JSON response',
      } as DataSubmissionEntity;

      // Wait for reactivity to update
      await nextTick();

      expect(parsedResponse.value).toEqual({
        'Error Message': [
          {
            Level: 'error',
            Message: 'Invalid JSON response',
            MessageCode: 'n/a',
          },
        ],
      });
    });
  });

  describe('updateParsedResponse', () => {
    it('should manually update parsed response', () => {
      const { parsedResponse, updateParsedResponse } = useApiFailureResponse(entityRef);

      const entity = {
        id: 1,
        apiFailureResponse: JSON.stringify({
          ManualTest: [{ Level: 'info', Message: 'Manual update test', MessageCode: 'INFO001' }],
        }),
      } as DataSubmissionEntity;

      updateParsedResponse(entity);

      expect(parsedResponse.value).toEqual({
        ManualTest: [{ Level: 'info', Message: 'Manual update test', MessageCode: 'INFO001' }],
      });
    });
  });
});

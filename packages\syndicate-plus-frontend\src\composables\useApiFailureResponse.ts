import { ref, watch, type Ref } from 'vue';
import type { DataSubmissionEntity, DataSubmissionEntityIssue } from '@core/interfaces';

export interface ParsedApiFailureResponse {
  [field: string]: Array<{
    Level: string;
    Message: string;
    MessageCode: string;
  }>;
}

export function useApiFailureResponse(entity: Ref<DataSubmissionEntity | null>) {
  const parsedResponse = ref<ParsedApiFailureResponse | null>(null);

  const parseApiFailureResponse = (apiFailureResponse: string): ParsedApiFailureResponse => {
    try {
      return JSON.parse(apiFailureResponse);
    } catch (error) {
      return {
        'Error Message': [
          {
            Level: 'error',
            Message: apiFailureResponse,
            MessageCode: 'n/a',
          },
        ],
      };
    }
  };

  const parseApiIssues = (issues: Array<DataSubmissionEntityIssue>): ParsedApiFailureResponse => {
    if (!issues || issues.length === 0) {
      return {};
    }
    const parsedIssues: ParsedApiFailureResponse = {};
    issues.forEach((issue) => {
      if (!parsedIssues[issue.field]) {
        parsedIssues[issue.field] = [];
      }
      parsedIssues[issue.field].push({
        Level: issue.severity === 1 ? 'error' : 'warning',
        Message: issue.message,
        MessageCode: 'n/a', // Assuming MessageCode is not provided in the issue
      });
    });
    return parsedIssues;
  };

  const updateParsedResponse = (newEntity: DataSubmissionEntity | null) => {
    if (newEntity?.issues?.length !== undefined || newEntity?.apiFailureResponse) {
      if (newEntity?.issues != undefined && newEntity.issues.length > 0) {
        parsedResponse.value = parseApiIssues(newEntity.issues);
      } else if (newEntity?.apiFailureResponse !== undefined) {
        parsedResponse.value = parseApiFailureResponse(newEntity.apiFailureResponse);
      } else {
        parsedResponse.value = null;
      }
    } else {
      parsedResponse.value = null;
    }
  };

  // Watch for entity changes
  watch(
    entity,
    (newEntity) => {
      updateParsedResponse(newEntity);
    },
    { immediate: true }
  );

  return {
    parsedResponse,
    parseApiFailureResponse,
    updateParsedResponse,
  };
}

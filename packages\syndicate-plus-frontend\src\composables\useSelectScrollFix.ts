import { onMounted, onUnmounted, Ref } from 'vue';

/**
 * Composable to fix CSelect dropdown positioning issues during scroll
 * This addresses the problem where CSelect dropdowns don't follow their parent elements during scroll
 *
 * @param selectRef - A ref to the CSelect component that needs the scroll fix
 * @returns cleanup function (automatically called on unmount)
 */
export function useSelectScrollFix(selectRef: Ref<any>) {
  const handleScroll = () => {
    // Try multiple methods to close the CSelect dropdown
    if (selectRef.value) {
      // Method 1: Try selectRef hidePopup
      if (selectRef.value.selectRef?.hidePopup) {
        selectRef.value.selectRef.hidePopup();
      }

      // Method 2: Try blur method on the select ref
      if (selectRef.value.selectRef?.blur) {
        selectRef.value.selectRef.blur();
      }

      // Method 3: Try to close via DOM manipulation
      const dropdowns = document.querySelectorAll('.q-menu') as NodeListOf<HTMLElement>;
      dropdowns.forEach((dropdown) => {
        if (dropdown.style.display !== 'none') {
          dropdown.style.display = 'none';
        }
      });
    }
  };

  const cleanup = () => {
    window.removeEventListener('scroll', handleScroll);
    document.removeEventListener('scroll', handleScroll, true);
  };

  onMounted(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
    document.addEventListener('scroll', handleScroll, { passive: true, capture: true });
  });

  onUnmounted(() => {
    cleanup();
  });

  return {
    cleanup,
  };
}

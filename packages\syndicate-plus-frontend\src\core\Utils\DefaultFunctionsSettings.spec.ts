import { describe, it, expect } from 'vitest';
import {
  defaultFunctionsSettings,
  checkIfFunctionRequiresSourceField,
  checkIfFunctionRequiresConfiguration,
  checkIfIsCustomFunction,
  type DefaultFunctionSettings,
} from './DefaultFunctionsSettings';
import { DefaultFunctionsName } from '@core/enums';

describe('DefaultFunctionsSettings', () => {
  describe('defaultFunctionsSettings object', () => {
    it('should contain all expected default functions', () => {
      const expectedFunctions = [
        DefaultFunctionsName.ToUpper,
        DefaultFunctionsName.SystemId,
        DefaultFunctionsName.Concatenate,
        DefaultFunctionsName.Constant,
        DefaultFunctionsName.ChooseLanguage,
        DefaultFunctionsName.CVLValue,
        DefaultFunctionsName.BeforeAfter,
        DefaultFunctionsName.SpecificationField,
        DefaultFunctionsName.RelatedEntityField,
        DefaultFunctionsName.Coalesce,
      ];

      expectedFunctions.forEach((functionName) => {
        expect(defaultFunctionsSettings[functionName]).toBeDefined();
        expect(defaultFunctionsSettings[functionName]).toMatchObject({
          displayName: expect.any(String),
          icon: expect.any(String),
          requiresSourceField: expect.any(Boolean),
          requiresConfiguration: expect.any(Boolean),
        });
      });
    });

    it('should have correct structure for each function setting', () => {
      Object.values(defaultFunctionsSettings).forEach((setting: DefaultFunctionSettings) => {
        expect(setting).toHaveProperty('displayName');
        expect(setting).toHaveProperty('icon');
        expect(setting).toHaveProperty('requiresSourceField');
        expect(setting).toHaveProperty('requiresConfiguration');

        expect(typeof setting.displayName).toBe('string');
        expect(typeof setting.icon).toBe('string');
        expect(typeof setting.requiresSourceField).toBe('boolean');
        expect(typeof setting.requiresConfiguration).toBe('boolean');

        // Validate that display names follow the expected pattern
        expect(setting.displayName).toMatch(/^core\.default_functions\./);

        // Validate that icons follow the Material Design Icons pattern
        expect(setting.icon).toMatch(/^mdi-/);
      });
    });

    it('should have specific configurations for known functions', () => {
      // Test specific function configurations
      expect(defaultFunctionsSettings[DefaultFunctionsName.ToUpper]).toEqual({
        displayName: 'core.default_functions.toUpper',
        icon: 'mdi-arrow-up-bold-circle-outline',
        requiresSourceField: true,
        requiresConfiguration: false,
      });

      expect(defaultFunctionsSettings[DefaultFunctionsName.SystemId]).toEqual({
        displayName: 'core.default_functions.systemId',
        icon: 'mdi-identifier',
        requiresSourceField: false,
        requiresConfiguration: false,
      });

      expect(defaultFunctionsSettings[DefaultFunctionsName.Concatenate]).toEqual({
        displayName: 'core.default_functions.concatenate.title',
        icon: 'mdi-plus-circle',
        requiresSourceField: false,
        requiresConfiguration: true,
      });

      expect(defaultFunctionsSettings[DefaultFunctionsName.Constant]).toEqual({
        displayName: 'core.default_functions.constant',
        icon: 'mdi-alpha-c-circle-outline',
        requiresSourceField: false,
        requiresConfiguration: true,
      });
    });
  });

  describe('checkIfFunctionRequiresSourceField', () => {
    it('should return true for functions that require source field', () => {
      const functionsRequiringSourceField = [
        DefaultFunctionsName.ToUpper,
        DefaultFunctionsName.ChooseLanguage,
        DefaultFunctionsName.CVLValue,
        DefaultFunctionsName.BeforeAfter,
      ];

      functionsRequiringSourceField.forEach((functionName) => {
        expect(checkIfFunctionRequiresSourceField(functionName)).toBe(true);
      });
    });

    it('should return false for functions that do not require source field', () => {
      const functionsNotRequiringSourceField = [
        DefaultFunctionsName.SystemId,
        DefaultFunctionsName.Concatenate,
        DefaultFunctionsName.Constant,
        DefaultFunctionsName.SpecificationField,
        DefaultFunctionsName.RelatedEntityField,
        DefaultFunctionsName.Coalesce,
      ];

      functionsNotRequiringSourceField.forEach((functionName) => {
        expect(checkIfFunctionRequiresSourceField(functionName)).toBe(false);
      });
    });

    it('should return false for non-existent function', () => {
      const nonExistentFunction = 'NonExistentFunction' as DefaultFunctionsName;
      expect(checkIfFunctionRequiresSourceField(nonExistentFunction)).toBe(false);
    });

    it('should handle all enum values correctly', () => {
      // Test all enum values to ensure consistency
      Object.values(DefaultFunctionsName).forEach((functionName) => {
        const result = checkIfFunctionRequiresSourceField(functionName);
        const expectedResult = defaultFunctionsSettings[functionName]?.requiresSourceField || false;
        expect(result).toBe(expectedResult);
      });
    });
  });

  describe('checkIfFunctionRequiresConfiguration', () => {
    it('should return true for functions that require configuration', () => {
      const functionsRequiringConfiguration = [
        DefaultFunctionsName.Concatenate,
        DefaultFunctionsName.Constant,
        DefaultFunctionsName.ChooseLanguage,
        DefaultFunctionsName.CVLValue,
        DefaultFunctionsName.BeforeAfter,
        DefaultFunctionsName.SpecificationField,
        DefaultFunctionsName.RelatedEntityField,
        DefaultFunctionsName.Coalesce,
      ];

      functionsRequiringConfiguration.forEach((functionName) => {
        expect(checkIfFunctionRequiresConfiguration(functionName)).toBe(true);
      });
    });

    it('should return false for functions that do not require configuration', () => {
      const functionsNotRequiringConfiguration = [DefaultFunctionsName.ToUpper, DefaultFunctionsName.SystemId];

      functionsNotRequiringConfiguration.forEach((functionName) => {
        expect(checkIfFunctionRequiresConfiguration(functionName)).toBe(false);
      });
    });

    it('should return false for non-existent function', () => {
      const nonExistentFunction = 'NonExistentFunction' as DefaultFunctionsName;
      expect(checkIfFunctionRequiresConfiguration(nonExistentFunction)).toBe(false);
    });

    it('should handle all enum values correctly', () => {
      // Test all enum values to ensure consistency
      Object.values(DefaultFunctionsName).forEach((functionName) => {
        const result = checkIfFunctionRequiresConfiguration(functionName);
        const expectedResult = defaultFunctionsSettings[functionName]?.requiresConfiguration || false;
        expect(result).toBe(expectedResult);
      });
    });
  });

  describe('checkIfIsCustomFunction', () => {
    it('should return false for all default functions', () => {
      Object.values(DefaultFunctionsName).forEach((functionName) => {
        expect(checkIfIsCustomFunction(functionName)).toBe(false);
      });
    });

    it('should return true for custom function names', () => {
      const customFunctionNames = [
        'CustomFunction1',
        'MyCustomFunction',
        'SpecialTransform',
        'DataProcessor',
        'ValidationFunction',
      ];

      customFunctionNames.forEach((functionName) => {
        expect(checkIfIsCustomFunction(functionName)).toBe(true);
      });
    });

    it('should return true for empty string', () => {
      expect(checkIfIsCustomFunction('')).toBe(true);
    });

    it('should return true for undefined converted to string', () => {
      expect(checkIfIsCustomFunction('undefined')).toBe(true);
    });

    it('should return true for null converted to string', () => {
      expect(checkIfIsCustomFunction('null')).toBe(true);
    });

    it('should be case sensitive', () => {
      // Test case sensitivity - these should be considered custom functions
      expect(checkIfIsCustomFunction('toupper')).toBe(true);
      expect(checkIfIsCustomFunction('TOUPPER')).toBe(true);
      expect(checkIfIsCustomFunction('concatenate')).toBe(true);
      expect(checkIfIsCustomFunction('CONSTANT')).toBe(true);
    });

    it('should handle special characters', () => {
      const specialCharacterNames = [
        'Custom-Function',
        'Custom_Function',
        'Custom.Function',
        'Custom Function',
        'Custom123',
        '123Custom',
      ];

      specialCharacterNames.forEach((functionName) => {
        expect(checkIfIsCustomFunction(functionName)).toBe(true);
      });
    });
  });

  describe('Edge cases and integration', () => {
    it('should handle all combinations of requiresSourceField and requiresConfiguration', () => {
      // Count actual combinations in the settings
      const actualCombinations: { [key: string]: number } = {};

      Object.values(defaultFunctionsSettings).forEach((setting) => {
        const key = `${setting.requiresSourceField}_${setting.requiresConfiguration}`;
        actualCombinations[key] = (actualCombinations[key] || 0) + 1;
      });

      // Verify we have at least one function for each meaningful combination
      expect(actualCombinations['false_false']).toBeGreaterThan(0); // SystemId
      expect(actualCombinations['true_false']).toBeGreaterThan(0); // ToUpper
      expect(actualCombinations['false_true']).toBeGreaterThan(0); // Concatenate, Constant, etc.
      expect(actualCombinations['true_true']).toBeGreaterThan(0); // ChooseLanguage, CVLValue, etc.
    });

    it('should maintain consistency between settings and helper functions', () => {
      Object.entries(defaultFunctionsSettings).forEach(([functionName, settings]) => {
        const enumValue = functionName as DefaultFunctionsName;

        expect(checkIfFunctionRequiresSourceField(enumValue)).toBe(settings.requiresSourceField);
        expect(checkIfFunctionRequiresConfiguration(enumValue)).toBe(settings.requiresConfiguration);
        expect(checkIfIsCustomFunction(functionName)).toBe(false);
      });
    });

    it('should have unique display names for all functions', () => {
      const displayNames = Object.values(defaultFunctionsSettings).map((setting) => setting.displayName);
      const uniqueDisplayNames = new Set(displayNames);
      expect(uniqueDisplayNames.size).toBe(displayNames.length);
    });

    it('should have unique icons for all functions', () => {
      const icons = Object.values(defaultFunctionsSettings).map((setting) => setting.icon);
      const uniqueIcons = new Set(icons);
      expect(uniqueIcons.size).toBe(icons.length);
    });

    it('should handle function classification correctly', () => {
      // Functions that need source field
      const sourceFieldFunctions = Object.entries(defaultFunctionsSettings)
        .filter(([, settings]) => settings.requiresSourceField)
        .map(([name]) => name);

      // Functions that need configuration
      const configurationFunctions = Object.entries(defaultFunctionsSettings)
        .filter(([, settings]) => settings.requiresConfiguration)
        .map(([name]) => name);

      // Simple functions (no source field, no configuration)
      const simpleFunctions = Object.entries(defaultFunctionsSettings)
        .filter(([, settings]) => !settings.requiresSourceField && !settings.requiresConfiguration)
        .map(([name]) => name);

      expect(sourceFieldFunctions.length).toBeGreaterThan(0);
      expect(configurationFunctions.length).toBeGreaterThan(0);
      expect(simpleFunctions.length).toBeGreaterThan(0);

      // Verify specific expected categorizations
      expect(simpleFunctions).toContain(DefaultFunctionsName.SystemId);
      expect(sourceFieldFunctions).toContain(DefaultFunctionsName.ToUpper);
      expect(configurationFunctions).toContain(DefaultFunctionsName.Constant);
    });
  });

  describe('Performance and robustness', () => {
    it('should handle rapid successive calls efficiently', () => {
      const startTime = performance.now();

      // Perform many operations
      for (let i = 0; i < 1000; i++) {
        checkIfFunctionRequiresSourceField(DefaultFunctionsName.ToUpper);
        checkIfFunctionRequiresConfiguration(DefaultFunctionsName.Concatenate);
        checkIfIsCustomFunction('CustomFunction');
      }

      const endTime = performance.now();

      // Should complete quickly (less than 10ms for 3000 operations)
      expect(endTime - startTime).toBeLessThan(10);
    });

    it('should be memory efficient for repeated access', () => {
      // Access the same settings multiple times to ensure no memory leaks
      const functionName = DefaultFunctionsName.ToUpper;

      for (let i = 0; i < 100; i++) {
        const setting = defaultFunctionsSettings[functionName];
        expect(setting).toBeDefined();
        expect(setting.displayName).toBe('core.default_functions.toUpper');
      }
    });
  });
});

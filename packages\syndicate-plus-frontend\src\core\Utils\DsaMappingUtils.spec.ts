import { describe, it, expect, vi } from 'vitest';
import { isDsaRowMapped } from './DsaMappingUtils';
import * as DefaultFunctionsSettings from './DefaultFunctionsSettings';

function makeRow(overrides: Partial<any> = {}) {
  return {
    ConverterArgs: null,
    ConverterClass: '',
    ConverterId: 0,
    inRiverEntityTypeId: '',
    inRiverDataType: '',
    inRiverFieldTypeId: '',
    inRiverFieldType: '',
    ...overrides,
  } as any;
}

describe('isDsaRowMapped', () => {
  it('returns true if row has inRiverFieldTypeId', () => {
    expect(isDsaRowMapped(makeRow({ inRiverFieldTypeId: 'foo' }))).toBe(true);
  });

  it('returns true if row has inRiverFieldType', () => {
    expect(isDsaRowMapped(makeRow({ inRiverFieldType: 'bar' }))).toBe(true);
  });

  it('returns true if row has custom function', () => {
    vi.spyOn(DefaultFunctionsSettings, 'checkIfIsCustomFunction').mockReturnValue(true);
    const row = makeRow({
      ConverterArgs: JSON.stringify({ transformations: [{ function: { name: 'customFunc' } }] }),
    });
    expect(isDsaRowMapped(row)).toBe(true);
  });

  it('returns true if row has default function that does not require source field', () => {
    vi.spyOn(DefaultFunctionsSettings, 'checkIfIsCustomFunction').mockReturnValue(false);
    vi.spyOn(DefaultFunctionsSettings, 'checkIfFunctionRequiresSourceField').mockReturnValue(false);
    const row = makeRow({
      ConverterArgs: JSON.stringify({ transformations: [{ function: { name: 'defaultFunc' } }] }),
    });
    expect(isDsaRowMapped(row)).toBe(true);
  });

  it('returns false if row has default function that requires source field', () => {
    vi.spyOn(DefaultFunctionsSettings, 'checkIfIsCustomFunction').mockReturnValue(false);
    vi.spyOn(DefaultFunctionsSettings, 'checkIfFunctionRequiresSourceField').mockReturnValue(true);
    const row = makeRow({
      ConverterArgs: JSON.stringify({ transformations: [{ function: { name: 'defaultFunc' } }] }),
    });
    expect(isDsaRowMapped(row)).toBe(false);
  });

  it('returns false if row is empty', () => {
    expect(isDsaRowMapped(makeRow())).toBe(false);
  });

  it('returns false if ConverterArgs is invalid JSON', () => {
    const row = makeRow({ ConverterArgs: '{invalidJson' });
    expect(isDsaRowMapped(row as any)).toBe(false);
  });
});

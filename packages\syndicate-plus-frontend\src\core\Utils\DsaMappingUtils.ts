import { checkIfIsCustomFunction, checkIfFunctionRequiresSourceField } from './DefaultFunctionsSettings';
import { DynamicMappingFieldResponse } from '@core/interfaces';

export function isDsaRowMapped(row: DynamicMappingFieldResponse): boolean {
  if (row.inRiverFieldTypeId || row.inRiverFieldType) {
    return true;
  }

  if (row.ConverterArgs) {
    try {
      const converterArgs = JSON.parse(row.ConverterArgs);
      const functionName = converterArgs?.transformations?.[0]?.function?.name;
      if (functionName) {
        if (checkIfIsCustomFunction(functionName)) {
          return true;
        }
        if (!checkIfFunctionRequiresSourceField(functionName)) {
          return true;
        }
      }
    } catch (e) {
      return false;
    }
  }

  return false;
}

<template>
  <teleport v-if="isTeleportEnabled" to="#right-sidebar">
    <c-tile-btn
      icon="mdi-content-save-outline"
      :icon-size="20"
      :tooltip-left="$t('syndicate_plus.common.save')"
      @click="onSave"
    />
    <c-tile-btn icon="mdi-close" :icon-size="20" :tooltip-left="$t('syndicate_plus.common.cancel')" @click="onCancel" />
  </teleport>
  <div class="flex flex-row flex-nowrap">
    <div class="collections-panel w-1/4 max-w-400px relative">
      <q-tabs
        v-model="tab"
        class="bg-white text-grey-7"
        active-color="primary"
        active-class="active-tab"
        indicator-color="transparent"
        align="justify"
        no-caps
      >
        <q-tab :name="AssignCollectionsTabNames.CHANNELS" :label="$t('core.assign_collections.channels')" />
        <q-tab :name="AssignCollectionsTabNames.WORKAREAS" :label="$t('core.assign_collections.workareas')" />
      </q-tabs>
      <q-tab-panels v-model="tab">
        <q-tab-panel :name="AssignCollectionsTabNames.CHANNELS" class="panel-content p-3">
          <q-inner-loading :showing="isChannelStructureLoading" color="primary" class="z-10">
            <c-spinner data-testid="channels-spinner" />
          </q-inner-loading>
          <c-select
            v-model="selectedChannel"
            :options="channels"
            data-testid="channels"
            :label="$t('core.assign_collections.channel')"
            option-label="DisplayName"
            option-value="Id"
            hide-bottom-space
            class="pb-10px"
            @update:model-value="onChangeChannel"
          />
          <c-inri-search
            v-model="filter"
            class="pb-10px"
            dense
            :placeholder="$t('syndicate_plus.common.filter.search')"
          />
          <q-tree
            v-model:expanded="expanded"
            :nodes="channelStructures"
            :filter="filter"
            no-connectors
            dense
            node-key="label"
          >
            <template #default-header="prop">
              <draggable
                :list="[prop.node]"
                :group="{
                  name: 'link',
                  pull: 'clone',
                  put: false,
                }"
                :sort="false"
                item-key="label"
                class="tree-node"
              >
                <template #item="{ element }">
                  <div class="tree-item cursor-pointer">
                    <div class="item-text">
                      {{ element.label }}
                      <q-tooltip>{{ element.label }}</q-tooltip>
                    </div>
                    <div class="triangle"></div>
                  </div>
                </template>
              </draggable>
            </template>
          </q-tree>
        </q-tab-panel>
        <q-tab-panel :name="AssignCollectionsTabNames.WORKAREAS" class="panel-content p-3">
          <q-inner-loading :showing="isWorkareaStructureLoading" color="primary" class="z-10">
            <c-spinner data-testid="workareas-spinner" />
          </q-inner-loading>
          <c-inri-search
            v-model="filter"
            class="pb-10px"
            dense
            :placeholder="$t('syndicate_plus.common.filter.search')"
          />
          <q-tree
            v-model:expanded="expanded"
            :nodes="workareaStructure"
            :filter="filter"
            no-connectors
            dense
            node-key="label"
          >
            <template #default-header="prop">
              <draggable
                :list="[prop.node]"
                :group="{
                  name: 'link',
                  pull: 'clone',
                  put: false,
                }"
                :sort="false"
                item-key="label"
                ghost-class="ghost"
                class="tree-node"
              >
                <template #item="{ element }">
                  <div class="tree-item cursor-pointer">
                    <div class="item-text">
                      {{ element.label }}
                      <q-tooltip>{{ element.label }}</q-tooltip>
                    </div>
                    <div class="triangle"></div>
                  </div>
                </template>
              </draggable>
            </template>
          </q-tree>
        </q-tab-panel>
      </q-tab-panels>
    </div>
    <c-section class="formats-panel w-3/4 relative">
      <q-inner-loading :showing="isCollectionLoading" color="primary" class="z-10">
        <c-spinner data-testid="formats-spinner" />
      </q-inner-loading>
      <q-table
        v-if="!!formatIds.length"
        data-testid="assign-collection-table"
        class="sticky-table-header"
        :pagination="{
          page: 1,
          rowsPerPage: 0,
        }"
        flat
        dense
        hide-bottom
        separator="cell"
        binary-state-sort
        :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
        :columns="assignCollectionsColumns"
        :hide-header="false"
        :rows="rows"
        row-key="Id"
        :rows-per-page-options="[0]"
      >
        <template #body="props">
          <q-tr
            :props="props"
            style="position: relative"
            :class="{
              changed: isDynamicFormat
                ? coreTradingPartnersStore.formatHasChanges(props.row.Id)
                : coreTradingPartnersStore.coreFormatHasChanges(props.row.Id),
            }"
            class="cursor-pointer"
          >
            <draggable
              v-model="dropzonePlaceholder"
              group="link"
              item-key="label"
              ghost-class="ghost"
              class="dropzone"
              :data-index="props.row.Id"
              @change="changeFormatRows"
              @dragover.prevent
              @dragenter.prevent
            >
              <template #item="{ element }">
                <div>{{ element }}</div>
              </template>
            </draggable>
            <q-td key="Name" :props="props">
              {{ props.row.Name }}
            </q-td>
            <q-td key="links" :props="props">
              <div v-if="props.row.links?.channels?.length">
                <q-chip
                  v-for="(channel, i) in props.row.links.channels"
                  :key="channel.ChannelNodeId"
                  v-model="props.row.links.channels[i]"
                  class="z-10"
                  removable
                  color="var(--color-primary)"
                  text-color="var(--color-grey-lighter)"
                  :label="
                    channel.channelNodeName
                      ? `${channel.channelNodeName} (${channel.channelName})`
                      : channel.channelName
                  "
                  :title="
                    channel.channelNodeName
                      ? `${channel.channelNodeName} (${channel.channelName})`
                      : channel.channelName
                  "
                  @remove="removeLink(channel, i)"
                />
              </div>
              <div v-if="props.row.links?.workareas?.length">
                <q-chip
                  v-for="(workarea, i) in props.row.links.workareas"
                  :key="workarea.workareaId"
                  v-model="props.row.links.workareas[i]"
                  class="z-10"
                  removable
                  color="var(--color-primary)"
                  text-color="var(--color-grey-lighter)"
                  :label="workarea.workareaName"
                  :title="workarea.workareaName"
                  @remove="removeLink(workarea, i)"
                />
              </div>
            </q-td>
          </q-tr>
        </template>
      </q-table>
    </c-section>
  </div>
</template>

<script setup lang="ts">
import { useCoreTradingPartnersStore, useChannelsStore, useCoreWorkareasStore } from '@core/stores';
import { storeToRefs } from 'pinia';
import { computed, onBeforeMount, onMounted, onUnmounted, ref, watchEffect } from 'vue';
import { assignCollectionsColumns } from '@core/const';
import { CInriSearch } from '@components';
import draggable from 'vuedraggable';
import { ChannelLinkResponse, CoreChannelLinkResponse } from '@core/interfaces';
import { notify } from '@inriver/inri';
import { useI18n } from 'vue-i18n';
import { extractTradingPartnerName } from '@core/services/utils';
import { AssignCollectionsTabNames } from '@core/enums';
import { CoreWorkareaLinkResponse, WorkareaLinkResponse } from '@core/interfaces/Workarea';

const props = defineProps({
  formatIds: {
    type: Array<number>,
    required: true,
  },
  isDynamicFormat: {
    type: Boolean,
    default: false,
  },
});

const { t } = useI18n();
const coreTradingPartnersStore = useCoreTradingPartnersStore();
const channelStore = useChannelsStore();
const workareasStore = useCoreWorkareasStore();

// Refs
const tab = ref(AssignCollectionsTabNames.CHANNELS);
const isTeleportEnabled = ref<boolean>(false);
const dropzonePlaceholder = ref([]);
const expanded = ref<string[]>([]);
const filter = ref<string>('');
const { dynamicFormats, coreFormats, isCollectionLoading } = storeToRefs(coreTradingPartnersStore);
const {
  channelStructures,
  selectedChannel,
  channels,
  isLoading: isChannelStructureLoading,
} = storeToRefs(channelStore);
const { isLoading: isWorkareaStructureLoading, workareaStructure } = storeToRefs(workareasStore);

// Computed
const rows = computed(() =>
  props.isDynamicFormat
    ? dynamicFormats.value?.filter((x) => props.formatIds?.includes(x.Id))
    : coreFormats.value?.filter((x) => props.formatIds?.includes(x.Id))
);

// Functions
const onChangeChannel = async () => {
  if (!selectedChannel.value) {
    return;
  }

  await channelStore.fetchChannelStructure(selectedChannel.value?.Id);
};

const changeFormatRows = (e) => {
  if (!e.added) {
    return;
  }

  if (tab.value === AssignCollectionsTabNames.CHANNELS) {
    const metadata = e.added.element.metadata;
    const isChannelNode = metadata.type === 'node';
    const channelNodeId = isChannelNode ? metadata.entityid : undefined;
    const channelNodeName = isChannelNode ? metadata.text : undefined;
    const channelId = isChannelNode ? Number.parseInt(metadata.id.split('/')[0]) : Number.parseInt(metadata.id);
    const channelName = isChannelNode ? channels.value.find((x) => x.Id === channelId)?.DisplayName : metadata.text;
    const link = {
      channelNodeId,
      channelNodeName,
      channelId,
      channelName,
    } as ChannelLinkResponse | CoreChannelLinkResponse;
    if (props.isDynamicFormat) {
      coreTradingPartnersStore.addLink(props.formatIds[0], link as ChannelLinkResponse);
    } else {
      coreTradingPartnersStore.addLinkToCoreFormat(props.formatIds[0], link as CoreChannelLinkResponse);
    }
  } else {
    const workareaId = e.added.element.metadata.id.replace(/^s_/, '');
    const workareaName = e.added.element.metadata.text;
    const link = {
      workareaId,
      workareaName,
    };
    if (props.isDynamicFormat) {
      coreTradingPartnersStore.addLink(props.formatIds[0], link as WorkareaLinkResponse);
    } else {
      coreTradingPartnersStore.addLinkToCoreFormat(props.formatIds[0], link as CoreWorkareaLinkResponse);
    }
  }

  dropzonePlaceholder.value = [];
};

const removeLink = (link, index: number) => {
  if (props.isDynamicFormat) {
    coreTradingPartnersStore.removeLink(link, props.formatIds[0], index);
    return;
  }

  coreTradingPartnersStore.removeLinkFromCoreFormat(link, props.formatIds[0], index);
};

const onSave = async () => {
  const allSuccessful = props.isDynamicFormat
    ? await coreTradingPartnersStore.saveLinksChanges()
    : await coreTradingPartnersStore.saveCoreFormatLinksChanges();

  if (allSuccessful) {
    notify.success(t('core.assign_collections.assign_success'), {
      position: 'bottom-right',
    });
  } else {
    notify.error(t('core.assign_collections.assign_error'), {
      position: 'bottom-right',
    });
  }

  await fetchCollections();
};

const onCancel = () =>
  props.isDynamicFormat
    ? coreTradingPartnersStore.cancelLinksChanges()
    : coreTradingPartnersStore.cancelFormatFileLinksChanges();

const fetchCollections = async () => {
  props.formatIds?.forEach(async (x) => {
    const format = props.isDynamicFormat
      ? dynamicFormats.value.find((y) => y.Id === x)
      : coreFormats.value.find((y) => y.Id === x);
    if (!format) {
      return;
    }

    const tradingPartnerName = extractTradingPartnerName(format.Name);
    if (props.isDynamicFormat) {
      await coreTradingPartnersStore.fetchCollections(tradingPartnerName);
      return;
    }

    await coreTradingPartnersStore.fetchCollectionsForCoreFormats(tradingPartnerName);
  });
};

// Lifecycle methods
onBeforeMount(async () => {
  await channelStore.fetch();
  await fetchCollections();
  await workareasStore.initStore();
});

onMounted(() => {
  isTeleportEnabled.value = true;
});

onUnmounted(() => coreTradingPartnersStore.clearStore());

watchEffect(() => {
  if (!!channels.value?.length) {
    expanded.value = channels.value.map((x) => x.DisplayName);
  }
});
</script>

<style lang="scss" scoped>
.collections-panel {
  height: 100vh;
  background-color: var(--on-primary-color);
  box-shadow: 2px 0 10px 0 var(--color-border);
  min-width: 280px;
}

.panel-content {
  height: calc(100vh - 180px);
  overflow-y: auto;
}

.tree-item {
  width: 100%;
  position: relative;
  padding: 7px 7px 7px 25px;
  border: 1px solid var(--color-grey);
  border-radius: 8px;
  background-color: var(--color-grey-10);
  margin-left: -25px;
  max-height: 35px;
}

.item-text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 90%;
}

.triangle {
  position: absolute;
  top: 50%;
  right: -10px;
  height: 25px;
  width: 25px;
  background-color: var(--color-grey-10);
  border: 1px solid var(--color-grey);
  transform: translateY(-50%) rotate(45deg);
  border-radius: 0px 5px 0px 0px;
  border-left: 0px;
  border-bottom: 0px;
}

.tree-node {
  width: 100%;
}

.dropzone {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  * {
    opacity: 0;
  }
}

.changed {
  background-color: var(--color-yellow-light);
}

:deep() {
  .q-tree__arrow {
    z-index: 10;
  }

  .q-field__bottom {
    display: none;
  }
}
</style>

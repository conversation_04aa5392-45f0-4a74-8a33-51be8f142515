import { describe, it, expect, vi } from 'vitest';
import { isWorkareaQuery } from './WorkareaService';
import * as coreServices from '@core/services';

describe('isWorkareaQuery', () => {
  it('returns true if workarea.isquery is true', async () => {
    vi.spyOn(coreServices, 'getWorkareaById').mockResolvedValue({ isquery: true });
    await expect(isWorkareaQuery('123')).resolves.toBe(true);
  });

  it('returns false if workarea.isquery is false', async () => {
    vi.spyOn(coreServices, 'getWorkareaById').mockResolvedValue({ isquery: false });
    await expect(isWorkareaQuery('456')).resolves.toBe(false);
  });

  it('throws if workarea is undefined', async () => {
    vi.spyOn(coreServices, 'getWorkareaById').mockResolvedValue(undefined);
    await expect(isWorkareaQuery('789')).rejects.toThrow('Workarea with ID 789 not found');
  });

  it('throws if getWorkareaById throws', async () => {
    vi.spyOn(coreServices, 'getWorkareaById').mockRejectedValue(new Error('DB error'));
    await expect(isWorkareaQuery('error')).rejects.toThrow('DB error');
  });
});

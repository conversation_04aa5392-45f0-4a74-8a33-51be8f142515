<template>
  <c-drop-down-menu data-test-id="core-export" icon="mdi-export">
    <template #items>
      <c-drop-down-menu-item :text="$t('core.review.title')" data-test-id="review" @click="onReview" />
      <c-drop-down-menu-item
        :text="$t('core.trading_partners.syndicate')"
        data-test-id="syndicate"
        @click="onSyndicate"
      />
      <c-drop-down-menu-item
        v-if="isDsaMappingEnabled"
        :text="$t('core.trading_partners.send_to_dsa')"
        data-test-id="send-to-dsa"
        @click="onSendToDsa"
      />
    </template>
  </c-drop-down-menu>
</template>

<script lang="ts" setup>
import { CDropDownMenu, CDropDownMenuItem } from '@core/components/Common';
import { useDsaMappingFeature } from '@core/composables';

// Define emits for review and syndicate actions
const emit = defineEmits(['review', 'syndicate', 'send-to-dsa']);

// Use the shared composable for DSA mapping feature flag
const { isDsaMappingEnabled } = useDsaMappingFeature();

// Functions to handle clicks
const onReview = () => emit('review');
const onSyndicate = () => emit('syndicate');
const onSendToDsa = () => emit('send-to-dsa');
</script>

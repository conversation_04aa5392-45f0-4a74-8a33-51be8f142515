<template>
  <c-panel
    :show="show"
    :title="$t('core.api_failure_response_dialog.title')"
    :show-close-button="true"
    @close="onClose"
  >
    <div v-if="!parsedResponse" class="no-data-message">
      {{ $t('core.api_failure_response_dialog.no_data_message') }}
    </div>
    <div v-else class="api-failure-content">
      <div class="error-messages">
        <div v-for="(errors, field) in parsedResponse" :key="field" class="error-section">
          <div v-if="Object.keys(parsedResponse).length > 1" class="field-header">
            {{ field }}
          </div>
          <div v-for="(error, index) in errors" :key="index" class="error-item">
            <div class="error-message">{{ error.Message }}</div>
            <div v-if="error.MessageCode !== 'n/a'" class="error-code">code: {{ error.MessageCode }}</div>
          </div>
        </div>
      </div>
    </div>
  </c-panel>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { DataSubmissionEntity } from '@core/interfaces';
import CPanel from '@core/components/CPanel.vue';
import { useApiFailureResponse } from '@composables/useApiFailureResponse';

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  entity: {
    type: Object as () => DataSubmissionEntity | null,
    required: true,
    default: null,
  },
});

const emit = defineEmits(['update:show']);

// Create a computed ref for the entity to pass to the composable
const entityRef = computed(() => props.entity);

// Use the composable
const { parsedResponse } = useApiFailureResponse(entityRef);

// Functions
const onClose = () => {
  emit('update:show', false);
};
</script>

<style lang="scss" scoped>
.no-data-message {
  padding: 24px;
  text-align: center;
  color: var(--color-grey-dark);
  font-size: 14px;
}

.api-failure-content {
  padding: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.error-messages {
  height: calc(100vh - 70px);
  overflow-y: auto;
  padding: 16px;
  padding-bottom: 16px;
}

.dialog-footer {
  padding: 16px;
  display: flex;
  justify-content: center;
  margin-top: auto;
}

.close-button {
  min-width: 120px;
  border-radius: 4px;
  text-transform: lowercase;
  font-weight: 400;
  background-color: #000;
  color: white;
  padding: 8px 16px;
}

.error-section {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.field-header {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 5px;
  padding-bottom: 0px;
}

.error-item {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.error-message {
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 4px;
  color: #000;
}

.error-code {
  font-size: 12px;
  color: #666;
}
</style>

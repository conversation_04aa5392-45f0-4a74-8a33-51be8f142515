<template>
  <div ref="tableContainer" class="table-container" @scroll="onScroll">
    <q-table
      ref="entitiesTable"
      v-model:selected="selectedEntity"
      class="submission-entities-table sticky-table-header"
      :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
      flat
      dense
      hide-bottom
      separator="cell"
      :pagination="{
        page: 1,
        rowsPerPage: 0,
      }"
      :rows="entities"
      row-key="id"
      :columns="submissionEntriesColumns"
      :loading="isLoading"
      @row-click="onRowClick"
      @row-dblclick="onRowDoubleClick"
    >
      <template #loading>
        <q-inner-loading showing color="primary" class="inner-loading">
          <c-spinner color="primary" size="40" />
        </q-inner-loading>
      </template>
      <template #body-cell-failureMessage="props">
        <q-td>{{ props.row.apiFailureResponse ? YesNo.YES : YesNo.NO }}</q-td>
      </template>
    </q-table>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { DataSubmissionEntity } from '@core/interfaces';
import { submissionEntriesColumns } from '@core/const';
import { YesNo } from '@core/enums';

const props = defineProps({
  entities: {
    type: Array as () => DataSubmissionEntity[],
    required: true,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  dataSubmissionId: {
    type: Number,
    required: true,
  },
});

const emit = defineEmits(['update:selected', 'load-more', 'show-details']);

// Refs
const selectedEntity = ref<DataSubmissionEntity[]>([]);
const tableContainer = ref<HTMLElement>();

// Functions
const onRowDoubleClick = (_: Event, row: DataSubmissionEntity): void => {
  // On double-click, open the dialog if there's an error message
  if (row.apiFailureResponse) {
    const newRowValue = row;
    selectedEntity.value = [newRowValue];
    emit('update:selected', newRowValue);
    emit('show-details');
  }
};

const onRowClick = (_: Event, row: DataSubmissionEntity): void => {
  const newRowValue = selectedEntity.value.includes(row) ? null : row;
  selectedEntity.value = newRowValue ? [newRowValue] : [];
  emit('update:selected', newRowValue || null);
};

const onScroll = (): void => {
  if (!tableContainer.value || props.isLoading) {
    return;
  }

  const { scrollTop, scrollHeight, clientHeight } = tableContainer.value;
  const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

  // Trigger load more when scrolled to 85% of the content
  if (scrollPercentage > 0.85 && props.entities.length > 0) {
    emit('load-more');
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  max-height: calc(100vh - 160px);
  min-height: 120px;
  overflow-y: auto;
}

.submission-entities-table {
  width: 100%;
}
</style>

<template>
  <section class="fields">
    <div display="flex">
      <c-inri-search
        v-model="searchText"
        class="c-inri-custom-search"
        dense
        :placeholder="$t('syndicate_plus.common.filter.search')"
      />
    </div>
    <c-separated-expansion-item v-for="category in categories" :key="category" :label="category" is-expanded>
      <draggable
        :list="getFieldsByCategory(category)"
        item-key="FormatField"
        ghost-class="builder-ghost"
        :group="{
          name: 'dsaFieldMapping',
          pull: 'clone',
          put: false,
        }"
        :sort="false"
        :move="dragStore.moveField"
        @start="dragStore.onDragStart"
      >
        <template #item="{ element }">
          <div class="content-map-item cursor-pointer">
            <div class="flex justify-between">
              <q-tooltip>{{ element.FormatField }}</q-tooltip>
              <div class="element-id">{{ element.FormatField }}</div>
              <div class="element-datatype">{{ element.FormatDataType }}</div>
            </div>
            <div class="triangle"></div>
          </div>
        </template>
      </draggable>
    </c-separated-expansion-item>
  </section>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { storeToRefs } from 'pinia';
import draggable from 'vuedraggable';
import { useEditFieldMappingStore, useEditFieldMappingDragStore } from '@core/stores';
import { CInriSearch } from '@components';
import { CSeparatedExpansionItem } from '@components/Shared';
import { MappingTargetFormatField } from '@core/interfaces/DsaMapping';

// Stores
const editFieldMappingStore = useEditFieldMappingStore();
const dragStore = useEditFieldMappingDragStore();
const { mapping } = storeToRefs(editFieldMappingStore);

// Refs
const searchText = ref('');

// Computed
const fields = computed(() => {
  if (!mapping.value || !mapping.value.MappingModelList) {
    return [];
  }

  // Filter out fields that don't have FormatField or have listItemOf property
  // TODO: handle FormatField with listItemOf property
  return mapping.value.MappingModelList.filter(
    (field) =>
      field.FormatField &&
      !field.listItemOf &&
      field.FormatDataType &&
      field.FormatField.toLowerCase().includes(searchText.value.toLowerCase())
  );
});

const categories = computed(() => {
  const categorySet = new Set<string>();
  fields.value.forEach((field) => {
    // If field has Category, use it, otherwise use "Uncategorized"
    const category = field.Category || 'Uncategorized';
    categorySet.add(category);
  });

  return Array.from(categorySet).sort();
});

// Functions
const getFieldsByCategory = (category: string): MappingTargetFormatField[] => {
  const filteredFields = fields.value.filter((field) => (field.Category || 'Uncategorized') === category);
  return filteredFields.map((field) => ({
    FormatField: field.FormatField,
    FormatFieldId: field.FormatFieldId || '',
    FormatDataType: field.FormatDataType,
  }));
};
</script>

<style lang="scss" scoped>
.search-container {
  padding: 8px 0;
}

.field-ghost {
  display: none;
}

.dragging {
  .field-ghost {
    display: block;
    margin-bottom: 10px;

    .mapping-row {
      border: 1px dashed var(--color-green);
    }
  }
}

.content-map-item {
  position: relative;
  padding: 7px;
  border: 1px dashed var(--color-grey);
  border-radius: 8px;
  background-color: var(--color-grey-10);
  margin: 5px 0px;
  font-size: 12px;
  height: 30px;
  cursor: grab;
}

.element-id {
  max-width: 70%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding-right: 5px;
}

.element-datatype {
  z-index: 2;
  padding-right: 7px;
  max-width: 30%;
}

.triangle {
  z-index: 1;
  position: absolute;
  top: 50%;
  right: -10px;
  height: 20px;
  width: 20px;
  background-color: var(--color-grey-10);
  border: 1px dashed var(--color-grey);
  transform: translateY(-50%) rotate(45deg);
  border-radius: 0px 5px 0px 0px;
  border-left: 0px;
  border-bottom: 0px;
}

:deep(.c-inri-input.c-inri-custom-search) {
  &.q-field--outlined .q-field__control {
    padding: 0px;
    border-radius: 0px;
    background: var(--color-grey-lighter);
    margin-bottom: 10px;

    &::after {
      border: 0 !important;
    }
  }

  &.c-inri-input--dense.q-field--outlined .q-field__control::before {
    border: 0 !important;
  }

  .q-field__inner .q-field__control {
    padding: 0px;
    padding-left: 10px;
    background: var(--color-grey-lighter);
    border-radius: 0px;

    .q-field__append span {
      width: 40px;
      height: 40px;
      border-left: 1px solid var(--surface-color);

      svg {
        height: 50%;
        width: 50%;
      }
    }
  }
}

:deep(.content-wrapper) {
  padding-left: 5px;
}
</style>

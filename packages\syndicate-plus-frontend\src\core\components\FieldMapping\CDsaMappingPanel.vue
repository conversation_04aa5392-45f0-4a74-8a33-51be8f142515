<template>
  <section class="dsa-mapping-panel">
    <c-mapping-buttons :config="buttonConfig" :is-visible="props.isVisible" />
    <div v-if="dsaMappingFields.length">
      <div class="dsa-editor">
        <q-table
          v-model:selected="selectedRows"
          :rows="dsaMappingFields"
          :row-key="(x) => x.FormatFieldId || x.FormatField"
          :columns="dsaMappingColumns"
          flat
          dense
          hide-bottom
          class="dsa-mapping-table sticky-table-header"
          :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
          :pagination="{
            page: 1,
            rowsPerPage: 0,
          }"
        >
          <template #body="props">
            <q-tr
              :props="props"
              :class="{
                focused: props.rowIndex === targetIndex,
              }"
              style="position: relative"
              class="cursor-pointer"
              @click="(e) => onItemClick(e, props.row)"
            >
              <draggable
                v-model="dropzonePlaceholder"
                group="dsaFieldMapping"
                item-key="FormatField"
                ghost-class="field-ghost"
                class="dropzone"
                :data-index="props.rowIndex"
                @change="changeMappingRows"
              >
                <template #item="{ element }">
                  <div>{{ element }}</div>
                </template>
              </draggable>
              <q-td key="inRiverFieldType" :props="props" class="source">
                {{ props.row.inRiverFieldType }}
              </q-td>
              <q-td key="inRiverDataType" :props="props" class="source">
                {{ props.row.inRiverDataType }}
              </q-td>
              <q-td key="actions" :props="props">
                <q-icon name="mdi-arrow-right" size="xs" class="grey-dark" />
              </q-td>
              <q-td key="FormatField" :props="props" class="target">
                {{ props.row.FormatField }}
              </q-td>
              <q-td key="FormatDataType" :props="props" class="target">
                <div style="z-index: 3; position: relative">
                  {{
                    props.row.UnitType
                      ? `${props.row.FormatDataType} (${props.row.UnitType})`
                      : props.row.FormatDataType
                  }}
                </div>
              </q-td>
              <q-td key="function" :props="props">
                <c-field-mapping-function :converter-args="props.row.ConverterArgs" />
              </q-td>
              <q-td key="Description" :props="props">
                <!-- TODO: Remove z-index (hide/show draggable) -->
                <q-icon v-if="!!props.row.Description" name="mdi-information" size="xs" style="z-index: 3">
                  <q-tooltip>
                    {{ props.row.Description }}
                  </q-tooltip>
                </q-icon>
              </q-td>
              <q-td key="Mandatory" :props="props">
                <div v-if="props.row.Mandatory" class="square">
                  <c-small-square :color="SquareColor.RED">
                    <q-tooltip>mandatory</q-tooltip>
                  </c-small-square>
                </div>
              </q-td>
              <q-td key="Recommended" :props="props">
                <div v-if="props.row.Recommended" class="square">
                  <c-small-square :color="SquareColor.PURPLE">
                    <q-tooltip>recommended</q-tooltip>
                  </c-small-square>
                </div>
              </q-td>
              <q-td key="Unique" :props="props">
                <div v-if="props.row.Unique" class="square">
                  <c-small-square v-if="props.row.Unique" :color="SquareColor.ORANGE">
                    <q-tooltip>unique</q-tooltip>
                  </c-small-square>
                </div>
              </q-td>
            </q-tr>
          </template>
        </q-table>
      </div>
      <c-base-function-configuration-dialog
        v-if="showFunctionDialog && dsaMapping && (!targetPanel || targetPanel === 'dsa')"
        :row-model="dsaMapping.MappingModelList[targetIndex!]"
        :function-model="(draggedElement as FunctionModel)"
        :is-new-function="(draggedElement as FunctionModel) !== undefined"
        @save-settings="saveSettings"
        @cancel="cancel"
      />
    </div>
    <div v-else class="text-grey-6 q-mt-md text-center">
      {{ $t('core.settings.field_mapping.target_to_dsa_placeholder') }}
    </div>
  </section>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import draggable from 'vuedraggable';
import { useEditFieldMappingDragStore } from '@core/stores';
import { useDsaMappingStore } from '@core/stores/useDsaMappingStore';
import { dsaMappingColumns } from '@core/const';
import { CSmallSquare } from '@components';
import { CFieldMappingFunction, CMappingButtons } from '@core/components/FieldMapping';
import { SquareColor } from '@enums';
import useLazySelectable from '@composables/useLazySelectable';
import { useFieldMappingButtons, MappingPanelType } from '@core/composables/FieldMapping/useFieldMappingButtons';
import { MappingTargetFormatField } from '@core/interfaces/DsaMapping';
import { isDsaRowMapped } from '@core/Utils/DsaMappingUtils';
import { checkIfFunctionRequiresSourceField } from '@core/Utils';
import { DynamicMappingFieldResponse, FunctionModel } from '@core/interfaces';
import { ConverterTransformation } from '@core/interfaces/FieldMapping/Functions';
import { CBaseFunctionConfigurationDialog } from '@core/components/FieldMapping/Functions';

// Props & Emits
const emit = defineEmits(['cancel-edit']);

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: true,
  },
  saveHandler: {
    type: Function,
    default: null,
  },
});

// Stores
const dsaMappingStore = useDsaMappingStore();
const dragStore = useEditFieldMappingDragStore();

// Refs
const isTeleportEnabled = ref(false);
const selectedRows = ref<DynamicMappingFieldResponse[]>([]);
const { targetIndex, targetPanel, isFunctionDragging, draggedElement, showFunctionDialog, showDialog } =
  storeToRefs(dragStore);
const { dsaMapping } = storeToRefs(dsaMappingStore);
const dropzonePlaceholder = ref([]);

// Computed
const dsaMappingFields = computed(() => {
  return dsaMapping.value?.MappingModelList || [];
});

// Composables
const { onItemClick } = useLazySelectable(dsaMappingFields, selectedRows, ref(0));

// DSA specific handler functions
const hasValidMapping = isDsaRowMapped;

const saveDsa = async () => {
  // Use the provided saveHandler if available, otherwise use the default DSA save logic
  if (props.saveHandler) {
    return await props.saveHandler();
  } else {
    // Default DSA save logic for backward compatibility
    if (!dsaMapping.value || !dsaMapping.value.MappingModelList.length) {
      return false;
    }

    if (!dsaMapping.value.MappingModelList.some((row) => hasValidMapping(row))) {
      return false;
    }

    // check if id exists update otherwise save new dsa mapping
    const success = await dsaMappingStore.saveNewDsaMapping();
    return success;
  }
};

const cancelEditDsa = () => {
  dsaMappingStore.resetDsaMapping();
  dragStore.clearStore();
  emit('cancel-edit');
};

const unmapSourceFieldDsa = () => {
  selectedRows.value?.forEach((selectedRow) => {
    const mappedRow = dsaMapping.value?.MappingModelList.find(filterMappingFields(selectedRow));
    if (!mappedRow) {
      return;
    }

    mappedRow.inRiverDataType = null;
    mappedRow.inRiverEntityTypeId = null;
    mappedRow.inRiverFieldTypeId = null;
    mappedRow.inRiverFieldType = null;

    if (
      mappedRow.ConverterArgs &&
      checkIfFunctionRequiresSourceField(JSON.parse(mappedRow.ConverterArgs).transformations[0].function.name)
    ) {
      unmapFunctionDsa();
    }
  });
};

const unmapFunctionDsa = () => {
  selectedRows.value?.forEach((selectedRow) => {
    const mappedRow = dsaMapping.value?.MappingModelList.find(filterMappingFields(selectedRow));
    if (!mappedRow) {
      return;
    }

    mappedRow.ConverterArgs = null;
    mappedRow.ConverterClass = null;
    mappedRow.ConverterId = null;
  });
};

const filterMappingFields = (selectedRow: DynamicMappingFieldResponse) => {
  const uniqueField = 'FormatField'; // TODO: check if can be replaced with FormatFieldId
  return (x) => x[uniqueField] === selectedRow[uniqueField];
};

const openFunctionSettings = () => {
  if (!selectedRows.value?.length) {
    return;
  }

  // Set the target panel type when opening function settings via click
  dragStore.setTargetPanel('dsa');
  targetIndex.value = dsaMapping.value?.MappingModelList.findIndex(filterMappingFields(selectedRows.value[0]));
  showFunctionDialog.value = true;
};

// Button configuration using our composable
const buttonConfig = useFieldMappingButtons({
  panelType: MappingPanelType.DSA,
  selectedRows,
  // Handler functions - DSA specific implementations
  saveHandler: saveDsa,
  cancelEditHandler: cancelEditDsa,
  unmapSourceFieldHandler: unmapSourceFieldDsa,
  unmapFunctionHandler: unmapFunctionDsa,
  openFunctionSettingsHandler: openFunctionSettings,
  // DSA panel doesn't need some of the standard panel features
  // openDefaultLanguageDialogHandler, importMappingHandler, exportMappingHandler, etc. can be undefined
});

// Functions
const changeMappingRows = (e) => {
  if (!e.added || !dsaMapping.value || targetIndex.value === undefined) {
    return;
  }

  // Set the target panel type for this drag operation
  dragStore.setTargetPanel('dsa');

  if (isFunctionDragging.value && draggedElement.value) {
    const functionRequiresSourceField = checkIfFunctionRequiresSourceField(
      (draggedElement.value as FunctionModel).Name as any
    );
    if (
      (functionRequiresSourceField && !!dsaMapping.value.MappingModelList[targetIndex.value].inRiverFieldTypeId) ||
      !functionRequiresSourceField
    ) {
      showFunctionDialog.value = true;
    } else {
      dragStore.clearStore();
    }
  } else {
    if (dsaMapping.value.MappingModelList[targetIndex.value].inRiverFieldTypeId) {
      showDialog.value = true;
    } else {
      mapElement();
    }
  }

  dropzonePlaceholder.value = [];
};

/**
 * Core mapping logic that applies field data to a DSA mapping row without cleanup
 * @param mappingField gets modified with extra field info
 * @param field the field to map
 */
const applyFieldToMappingRow = (mappingField: DynamicMappingFieldResponse, field: MappingTargetFormatField) => {
  const sourceFieldData = {
    inRiverDataType: field.FormatDataType, // format.dataType
    inRiverEntityTypeId: null, // this value can be null for DSA mappings
    inRiverFieldTypeId: field.FormatField, // format.field
    inRiverFieldType: field.FormatField, // format.field
  };
  Object.assign(mappingField, sourceFieldData);
};

const mapElement = () => {
  if (!dsaMapping.value || targetIndex.value === undefined || !draggedElement.value) {
    return;
  }

  const element = draggedElement.value as MappingTargetFormatField;

  // Check if multiple rows are selected and the target is one of the selected rows
  const targetRow = dsaMappingFields.value[targetIndex.value];
  const isTargetInSelection = selectedRows.value.some((selected) => {
    return selected.FormatField === targetRow.FormatField;
  });

  if (selectedRows.value.length > 1 && isTargetInSelection) {
    // Bulk mapping: map to all selected rows
    selectedRows.value.forEach((selectedRow) => {
      const actualRow = dsaMapping.value?.MappingModelList.find((row) => {
        return row.FormatField === selectedRow.FormatField;
      });

      if (actualRow) {
        applyFieldToMappingRow(actualRow, element);
      }
    });

    // Clean up after bulk mapping
    dragStore.clearStore();
    selectedRows.value = [];
  } else {
    // Single mapping: map to the target row only
    mapElementWithRow(dsaMappingFields.value[targetIndex.value], element);
  }
};

const mapElementWithRow = (mappingField: DynamicMappingFieldResponse, field: MappingTargetFormatField) => {
  applyFieldToMappingRow(mappingField, field);

  dragStore.clearStore();
  selectedRows.value = [];
};

const saveSettings = (settings: ConverterTransformation) => {
  // Only handle saves if this is the target panel or no panel is specified (for backward compatibility)
  if (targetPanel.value && targetPanel.value !== 'dsa') {
    return;
  }

  if (!!settings && dsaMapping.value && targetIndex.value !== undefined) {
    dsaMapping.value.MappingModelList[targetIndex.value].ConverterArgs = JSON.stringify({
      transformations: [settings],
    });

    if (draggedElement.value) {
      dsaMapping.value.MappingModelList[targetIndex.value].ConverterId = (draggedElement.value as FunctionModel).Id;
    }
  }

  dragStore.clearStore();
};

const cancel = () => {
  showDialog.value = false;
  dragStore.clearStore();
};

// Lifecycle methods
onMounted(() => {
  isTeleportEnabled.value = true;
});
</script>

<style lang="scss" scoped>
.dsa-mapping-panel {
  height: 100%;
}

.dsa-mapping-table {
  padding: 10px 0px;
}

.dsa-editor {
  max-height: calc(100vh - 200px);
  position: relative;
  overflow: auto;
}

.dropzone {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  * {
    opacity: 0;
  }
}

.mapped {
  border: 1px black solid;
}

.label {
  width: 31px;
  height: 31px;

  &.mandatory {
    background-color: var(--color-orange);
  }

  &.recommended {
    background-color: var(--color-blue-light);
  }

  &.unique {
    background-color: var(--color-yellow);
  }
}

.square {
  z-index: 3;
  position: relative;
}

:deep(.sticky-table-header) {
  thead tr th {
    position: sticky;
    z-index: 4;
  }
}

:deep(.c-inri-input.c-inri-custom-search) {
  &.q-field--outlined .q-field__control {
    padding: 0px;
    border-radius: 0px;
    background: var(--color-grey-lighter);
    margin-bottom: 10px;

    &::after {
      border: 0 !important;
    }
  }

  &.c-inri-input--dense.q-field--outlined .q-field__control::before {
    border: 0 !important;
  }

  .q-field__inner .q-field__control {
    padding: 0px;
    padding-left: 10px;
    background: var(--color-grey-lighter);
    border-radius: 0px;

    .q-field__append span {
      width: 40px;
      height: 40px;
      border-left: 1px solid var(--surface-color);

      svg {
        height: 50%;
        width: 50%;
      }
    }
  }
}
</style>

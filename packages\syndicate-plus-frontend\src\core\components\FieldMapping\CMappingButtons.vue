<template>
  <teleport v-if="isTeleportEnabled && isVisible" to="#right-sidebar">
    <c-tile-btn
      icon="mdi-content-save-outline"
      :icon-size="20"
      :tooltip-left="$t('syndicate_plus.common.save')"
      @click="config.handlers.save"
    />
    <c-tile-btn
      icon="mdi-close"
      :icon-size="20"
      :tooltip-left="$t('syndicate_plus.common.cancel')"
      @click="config.handlers.cancelEdit"
    />
    <c-tile-btn
      v-if="config.visibility.isAutomapButtonVisible.value"
      icon="mdi-auto-fix"
      :icon-size="20"
      :tooltip-left="$t('core.settings.field_mapping.map_automatically')"
      @click="config.handlers.mapAutomatically"
    />
    <c-tile-btn
      v-if="config.visibility.isSetDefaultLanguageButtonVisible.value && config.handlers.openDefaultLanguageDialog"
      icon="mdi-refresh-auto"
      :icon-size="20"
      :tooltip-left="$t('core.settings.field_mapping.set_default_language')"
      @click="config.handlers.openDefaultLanguageDialog"
    />
    <c-tile-btn
      v-if="config.visibility.isImportMappingButtonVisible.value && config.handlers.importMappingIntoEditor"
      icon="mdi-import"
      :icon-size="20"
      :tooltip-left="$t('core.settings.field_mapping.import')"
      @click="config.handlers.importMappingIntoEditor"
    />
    <c-tile-btn
      v-if="config.visibility.isExportMappingButtonVisible.value && config.handlers.exportCurrentMapping"
      icon="mdi-export"
      :icon-size="20"
      :tooltip-left="$t('core.settings.field_mapping.export')"
      @click="config.handlers.exportCurrentMapping"
    />
    <c-tile-btn
      v-if="config.visibility.isUnmapSourceFieldButtonVisible.value"
      icon="mdi-link-off"
      :icon-size="20"
      :tooltip-left="config.tooltips.unmapSourceFieldTooltip.value"
      @click="config.handlers.unmapSourceField"
    />
    <c-tile-btn
      v-if="config.visibility.isEditFunctionSettingsButtonVisible.value && config.handlers.openFunctionSettings"
      icon="mdi-alpha-f-circle-outline"
      :icon-size="20"
      :tooltip-left="$t('core.settings.field_mapping.edit_function')"
      @click="config.handlers.openFunctionSettings"
    />
    <c-tile-btn
      v-if="config.visibility.isUnmapFunctionButtonVisible.value"
      icon="mdi-close-circle-outline"
      :icon-size="20"
      :tooltip-left="$t('core.settings.field_mapping.unmap_function')"
      @click="config.handlers.unmapFunction"
    />
    <c-tile-btn
      v-if="config.visibility.isMapEnumButtonVisible.value && config.handlers.openMapEnumDialog"
      icon="mdi-alpha-e-box-outline"
      :icon-size="20"
      :tooltip-left="$t('core.settings.field_mapping.map_enum')"
      @click="config.handlers.openMapEnumDialog"
    />
    <c-tile-btn
      v-if="config.visibility.isAddListItemButtonVisible.value && config.handlers.addListItem"
      icon="mdi-playlist-plus"
      :icon-size="20"
      :tooltip-left="$t('core.settings.field_mapping.add_list_item')"
      @click="config.handlers.addListItem"
    />
    <c-tile-btn
      v-if="config.visibility.isRemoveListItemButtonVisible.value && config.handlers.removeListItem"
      icon="mdi-playlist-minus"
      :icon-size="20"
      :tooltip-left="$t('core.settings.field_mapping.remove_list_item')"
      @click="config.handlers.removeListItem"
    />
  </teleport>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { FieldMappingButtonConfig } from '@core/composables/FieldMapping/useFieldMappingButtons';

defineProps<{
  config: FieldMappingButtonConfig;
  isVisible?: boolean;
}>();

const isTeleportEnabled = ref(false);

onMounted(() => {
  isTeleportEnabled.value = true;
});
</script>

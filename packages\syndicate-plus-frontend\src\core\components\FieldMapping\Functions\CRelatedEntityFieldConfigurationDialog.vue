<template>
  <section class="c-inri-section">
    <q-form ref="form" greedy autofocus>
      <c-grid :cols="{ md: 1 }">
        <c-dialog
          v-model="showDialog"
          class="c-dialog"
          :title="$t('core.default_functions.related_entity_field.title')"
          :confirm-button-text="$t('syndicate_plus.common.apply')"
          :is-loading="isLoading"
          :disable-confirm="!relatedEntitiesExist"
          :disabled-confirm-tooltip="disableConfirmTooltip"
          @confirm="confirm"
          @cancel="cancelDialog"
        >
          <div v-if="currentModel?.mainEntityType">
            <c-select
              v-model="currentModel.mainEntityType"
              :options="mappingEntityTypes"
              :label="$t('core.default_functions.related_entity_field.entity_type')"
              :placeholder="$t('core.default_functions.related_entity_field.entity_type')"
              option-key="Id"
              option-label="Id"
              hide-bottom-space
              @update:model-value="onMainEntityTypeChange"
            />
            <div v-for="(level, index) in currentModel.relations" :key="index" class="flex flex-row flex-nowrap">
              <c-select
                v-model="level.relation"
                class="w-1/5"
                v-bind="$inri.input"
                :options="Relationship"
                :label="$t('core.default_functions.related_entity_field.relationship')"
                use-input
                @update:model-value="onRelationChange(index)"
              />
              <c-select
                v-model="level.relative"
                class="w-4/5"
                v-bind="$inri.input"
                :options="getRelativesByIndex(index)"
                option-key="Id"
                :option-label="getOptionLabel"
                :label="
                  $t('core.default_functions.related_entity_field.related_entity_type', { entityTypeIndex: index + 1 })
                "
                use-input
                @update:model-value="onEntityTypeChange(index)"
              />
              <c-tile-btn
                v-if="isRemoveButtonVisible && index > 0"
                icon="mdi-delete-outline"
                class="action-button"
                :tooltip-right="$t('core.default_functions.related_entity_field.remove_relation')"
                :icon-size="20"
                @click="removeRelation(index)"
              />
              <c-tile-btn
                v-if="isAddButtonVisible"
                icon="mdi-plus-outline"
                class="action-button"
                :tooltip-right="$t('core.default_functions.related_entity_field.add_relation')"
                :icon-size="20"
                @click="addRelation"
              />
            </div>
          </div>
          <div v-if="currentFieldTypes.length && currentModel">
            <c-select
              v-if="relatedEntities.length"
              v-model="currentModel.entity"
              :options="relatedEntities"
              :label="$t('core.default_functions.related_entity_field.entity')"
              :placeholder="$t('core.default_functions.related_entity_field.entity')"
              option-label="Value"
              hide-bottom-space
            />
            <c-select
              v-model="currentModel.field"
              :options="currentFieldTypes"
              :label="$t('core.default_functions.related_entity_field.field')"
              :placeholder="$t('core.default_functions.related_entity_field.field')"
              :option-label="getFieldTypeDisplayName"
              hide-bottom-space
            />
            <c-select
              v-if="selectedFieldTypeIsLocaleString"
              v-model="currentModel.language"
              :options="languages"
              :label="$t('syndicate_plus.common.language')"
              :placeholder="$t('syndicate_plus.common.language')"
              option-label="DisplayName"
              option-value="Name"
              hide-bottom-space
            />
          </div>
        </c-dialog>
      </c-grid>
    </q-form>
  </section>
</template>

<script setup lang="ts">
import { computed, onBeforeMount } from 'vue';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import { useDialog } from '@inriver/inri';
import { FunctionSettingsComponentEmits } from '@core/interfaces/FieldMapping/Functions';
import { useLanguages } from '@core/composables/Common';
import { useRelatedEntityFieldFunction } from '@core/composables/FieldMapping/Functions';
import { InriverFieldType, Relative } from '@core/interfaces';
import { useEntityTypesStore } from '@core/stores';
import { Relationship } from '@core/const';

const props = defineProps({
  converterArgs: {
    type: String,
    required: false,
    default: undefined,
  },
});

const emit = defineEmits<FunctionSettingsComponentEmits>();
const entityTypesStore = useEntityTypesStore();

// Refs
const { entityTypes } = storeToRefs(entityTypesStore);

// Composables
const { showDialog, cancel, confirmSuccess } = useDialog();
const { languages } = useLanguages();
const { t } = useI18n();

const {
  settings,
  selectedFieldTypeIsLocaleString,
  currentModel,
  currentFieldTypes,
  relatedEntities,
  isAddButtonVisible,
  isRemoveButtonVisible,
  mappingEntityTypes,
  onRelationChange,
  onEntityTypeChange,
  onMainEntityTypeChange,
  onDialogFormInit,
  getRelativesByIndex,
  addRelation,
  removeRelation,
  isLoading,
} = useRelatedEntityFieldFunction(entityTypes, props.converterArgs);

// Computed
const relatedEntitiesExist = computed(() => !!relatedEntities.value.length);

const disableConfirmTooltip = computed(() =>
  !relatedEntitiesExist.value ? t('core.default_functions.related_entity_field.no_entities_tooltip') : ''
);

// Functions
const getOptionLabel = (entityType: Relative) =>
  entityType.LinkEntityTypeId ? `${entityType.Id} (${entityType.LinkEntityTypeId})` : `${entityType.Id}`;

const getFieldTypeDisplayName = (fieldType: InriverFieldType) => `${fieldType.id} (${fieldType.dataType})`;

const confirm = async () => {
  emit('saveSettings', settings.value);
  confirmSuccess(null);
};

const cancelDialog = async () => {
  emit('cancel');
  cancel();
};

// Lifecycle methods
onBeforeMount(async () => {
  await entityTypesStore.fetchEntityTypes();
  if (entityTypes.value.length) {
    await onDialogFormInit();
  }
});
</script>

<style scoped lang="scss">
.action-button {
  width: 41px;
  height: 41px;
}
</style>

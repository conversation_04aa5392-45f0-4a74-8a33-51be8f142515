import { describe, it, expect, vi, beforeEach, type Mock } from 'vitest';
import { ref } from 'vue';
import { useCvlOptions, type CvlOption } from './useCvlOptions';
import type {
  CustomFunctionArgumentGroups,
  CustomFunctionArgument,
} from '@core/components/FieldMapping/customFunctions/CustomFunctionArgumentGroups';

// Mock the external dependencies
vi.mock('@core/services/Cvl');
vi.mock('@core/components/FieldMapping/customFunctions/extractCustomFunctionCvls');

import { getCvlValuesByCvlId } from '@core/services/Cvl';
import { extractCustomFunctionCvls } from '@core/components/FieldMapping/customFunctions/extractCustomFunctionCvls';

const mockGetCvlValuesByCvlId = getCvlValuesByCvlId as Mock;
const mockExtractCustomFunctionCvls = extractCustomFunctionCvls as Mock;

describe('useCvlOptions', () => {
  const mockCvlOptions: CvlOption[] = [
    {
      Id: 25481,
      CVLId: 'ResourceType',
      Key: 'image',
      DisplayValue: 'Image',
    },
    {
      Id: 25482,
      CVLId: 'ResourceType',
      Key: 'small_image',
      DisplayValue: 'Small Image',
    },
    {
      Id: 25734,
      CVLId: 'Locale',
      Key: 'en-En',
      DisplayValue: 'English',
    },
  ];

  const mockArgumentList: CustomFunctionArgumentGroups = {
    argumentGroups: [
      {
        arguments: [
          {
            name: 'ResourceType',
            type: 'CVL#ResourceType',
            defaultValue: 'image',
            value: 'image',
          },
          {
            name: 'Locale',
            type: 'CVL#Locale',
            defaultValue: 'en-En',
            value: 'en-En',
          },
          {
            name: 'TextInput',
            type: 'string',
            value: 'test',
          },
        ],
      },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('isCvlType', () => {
    it('should return true for CVL type arguments', () => {
      const argumentListRef = ref(mockArgumentList);
      const { isCvlType } = useCvlOptions(argumentListRef);

      const cvlArgument: CustomFunctionArgument = {
        name: 'test',
        type: 'CVL#ResourceType',
      };

      expect(isCvlType(cvlArgument)).toBe(true);
    });

    it('should return false for non-CVL type arguments', () => {
      const argumentListRef = ref(mockArgumentList);
      const { isCvlType } = useCvlOptions(argumentListRef);

      const nonCvlArgument: CustomFunctionArgument = {
        name: 'test',
        type: 'string',
      };

      expect(isCvlType(nonCvlArgument)).toBe(false);
    });

    it('should return false when type is undefined', () => {
      const argumentListRef = ref(mockArgumentList);
      const { isCvlType } = useCvlOptions(argumentListRef);

      const argumentWithoutType: CustomFunctionArgument = {
        name: 'test',
        type: undefined as any,
      };

      expect(isCvlType(argumentWithoutType)).toBe(false);
    });

    it('should handle case insensitive CVL prefix', () => {
      const argumentListRef = ref(mockArgumentList);
      const { isCvlType } = useCvlOptions(argumentListRef);

      const cvlArgument: CustomFunctionArgument = {
        name: 'test',
        type: 'cvl#ResourceType',
      };

      expect(isCvlType(cvlArgument)).toBe(true);
    });
  });

  describe('getCvlType', () => {
    it('should extract CVL type from argument type', () => {
      const argumentListRef = ref(mockArgumentList);
      const { getCvlType } = useCvlOptions(argumentListRef);

      const cvlArgument: CustomFunctionArgument = {
        name: 'test',
        type: 'CVL#ResourceType',
      };

      expect(getCvlType(cvlArgument)).toBe('ResourceType');
    });

    it('should return empty string for non-CVL arguments', () => {
      const argumentListRef = ref(mockArgumentList);
      const { getCvlType } = useCvlOptions(argumentListRef);

      const nonCvlArgument: CustomFunctionArgument = {
        name: 'test',
        type: 'string',
      };

      expect(getCvlType(nonCvlArgument)).toBe('');
    });
  });

  describe('loadCvlData', () => {
    it('should load CVL data for all CVL types in parallel', async () => {
      const argumentListRef = ref(mockArgumentList);
      const { loadCvlData, cvlOptionValues, isCvlDataLoading } = useCvlOptions(argumentListRef);

      mockExtractCustomFunctionCvls.mockReturnValue(['ResourceType', 'Locale']);
      mockGetCvlValuesByCvlId.mockImplementation((cvlType: string) => {
        if (cvlType === 'ResourceType') {
          return Promise.resolve(mockCvlOptions.filter((opt) => opt.CVLId === 'ResourceType'));
        }
        if (cvlType === 'Locale') {
          return Promise.resolve(mockCvlOptions.filter((opt) => opt.CVLId === 'Locale'));
        }
        return Promise.resolve([]);
      });

      expect(isCvlDataLoading.value).toBe(false);

      const loadPromise = loadCvlData(mockArgumentList);
      expect(isCvlDataLoading.value).toBe(true);

      await loadPromise;

      expect(isCvlDataLoading.value).toBe(false);
      expect(mockGetCvlValuesByCvlId).toHaveBeenCalledTimes(2);
      expect(mockGetCvlValuesByCvlId).toHaveBeenCalledWith('ResourceType');
      expect(mockGetCvlValuesByCvlId).toHaveBeenCalledWith('Locale');
      expect(cvlOptionValues.value).toHaveProperty('ResourceType');
      expect(cvlOptionValues.value).toHaveProperty('Locale');
    });

    it('should handle errors and still clear loading state', async () => {
      const argumentListRef = ref(mockArgumentList);
      const { loadCvlData, isCvlDataLoading } = useCvlOptions(argumentListRef);

      mockExtractCustomFunctionCvls.mockReturnValue(['ResourceType']);
      mockGetCvlValuesByCvlId.mockRejectedValue(new Error('API Error'));

      // Suppress console.error for this test to avoid noise in test output
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {
        // Intentionally empty to suppress console output during test
      });

      expect(isCvlDataLoading.value).toBe(false);

      await expect(loadCvlData(mockArgumentList)).rejects.toThrow('API Error');
      expect(isCvlDataLoading.value).toBe(false);

      // Restore console.error
      consoleErrorSpy.mockRestore();
    });

    it('should not load data when no CVL types are found', async () => {
      const argumentListRef = ref(mockArgumentList);
      const { loadCvlData, isCvlDataLoading } = useCvlOptions(argumentListRef);

      mockExtractCustomFunctionCvls.mockReturnValue([]);

      await loadCvlData(mockArgumentList);

      expect(isCvlDataLoading.value).toBe(false);
      expect(mockGetCvlValuesByCvlId).not.toHaveBeenCalled();
    });
  });

  describe('selectedOptions', () => {
    it('should compute selected options based on argument values', async () => {
      const argumentListRef = ref(mockArgumentList);
      const { loadCvlData, selectedOptions } = useCvlOptions(argumentListRef);

      mockExtractCustomFunctionCvls.mockReturnValue(['ResourceType', 'Locale']);
      mockGetCvlValuesByCvlId.mockImplementation((cvlType: string) => {
        return Promise.resolve(mockCvlOptions.filter((opt) => opt.CVLId === cvlType));
      });

      await loadCvlData(mockArgumentList);

      expect(selectedOptions.value['0-0']).toEqual(
        expect.objectContaining({
          Key: 'image',
          DisplayValue: 'Image',
        })
      );
      expect(selectedOptions.value['0-1']).toEqual(
        expect.objectContaining({
          Key: 'en-En',
          DisplayValue: 'English',
        })
      );
    });

    it('should use defaultValue when value is not set', async () => {
      const argumentListRef = ref({
        argumentGroups: [
          {
            arguments: [
              {
                name: 'ResourceType',
                type: 'CVL#ResourceType',
                defaultValue: 'image',
                value: undefined,
              },
            ],
          },
        ],
      });
      const { loadCvlData, selectedOptions } = useCvlOptions(argumentListRef);

      mockExtractCustomFunctionCvls.mockReturnValue(['ResourceType']);
      mockGetCvlValuesByCvlId.mockResolvedValue(mockCvlOptions.filter((opt) => opt.CVLId === 'ResourceType'));

      await loadCvlData(argumentListRef.value);

      expect(selectedOptions.value['0-0']).toEqual(
        expect.objectContaining({
          Key: 'image',
          DisplayValue: 'Image',
        })
      );
    });
  });

  describe('getSelectedOption', () => {
    it('should return the correct selected option for given indices', async () => {
      const argumentListRef = ref(mockArgumentList);
      const { loadCvlData, getSelectedOption } = useCvlOptions(argumentListRef);

      mockExtractCustomFunctionCvls.mockReturnValue(['ResourceType']);
      mockGetCvlValuesByCvlId.mockResolvedValue(mockCvlOptions.filter((opt) => opt.CVLId === 'ResourceType'));

      await loadCvlData(mockArgumentList);

      const selectedOption = getSelectedOption(0, 0);
      expect(selectedOption).toEqual(
        expect.objectContaining({
          Key: 'image',
          DisplayValue: 'Image',
        })
      );
    });

    it('should return null for non-existent option', () => {
      const argumentListRef = ref(mockArgumentList);
      const { getSelectedOption } = useCvlOptions(argumentListRef);

      const selectedOption = getSelectedOption(999, 999);
      expect(selectedOption).toBeNull();
    });
  });

  describe('updateArgumentValue', () => {
    it('should update argument value with selected option key', () => {
      const argumentListRef = ref(mockArgumentList);
      const { updateArgumentValue } = useCvlOptions(argumentListRef);

      const argument: CustomFunctionArgument = {
        name: 'test',
        type: 'CVL#ResourceType',
        value: 'old_value',
      };

      const selectedOption: CvlOption = {
        Id: 123,
        CVLId: 'ResourceType',
        Key: 'new_value',
        DisplayValue: 'New Value',
      };

      updateArgumentValue(argument, selectedOption);

      expect(argument.value).toBe('new_value');
    });

    it('should set argument value to undefined when selectedOption is null', () => {
      const argumentListRef = ref(mockArgumentList);
      const { updateArgumentValue } = useCvlOptions(argumentListRef);

      const argument: CustomFunctionArgument = {
        name: 'test',
        type: 'CVL#ResourceType',
        value: 'old_value',
      };

      updateArgumentValue(argument, null);

      expect(argument.value).toBeUndefined();
    });
  });
});

import { ref, computed, type Ref } from 'vue';
import { getCvlValuesByCvlId } from '@core/services/Cvl';
import { extractCustomFunctionCvls } from '@core/components/FieldMapping/customFunctions/extractCustomFunctionCvls';
import {
  CustomFunctionArgument,
  CustomFunctionArgumentGroups,
} from '@core/components/FieldMapping/customFunctions/CustomFunctionArgumentGroups';

export interface CvlOption {
  Key: string;
  DisplayValue: string;
  Id: number;
  CVLId: string;
}

export interface UseCvlOptionsReturn {
  cvlOptionValues: Ref<Record<string, CvlOption[]>>;
  isCvlDataLoading: Ref<boolean>;
  selectedOptions: Ref<Record<string, CvlOption | null>>;
  loadCvlData: (argumentList: CustomFunctionArgumentGroups) => Promise<void>;
  getSelectedOption: (groupIndex: number, argIndex: number) => CvlOption | null;
  updateArgumentValue: (argument: CustomFunctionArgument, selectedOption: CvlOption | null) => void;
  isCvlType: (argument: CustomFunctionArgument) => boolean;
  getCvlType: (argument: CustomFunctionArgument) => string;
}

export function useCvlOptions(argumentListRef: Ref<CustomFunctionArgumentGroups>): UseCvlOptionsReturn {
  const cvlOptionValues = ref<Record<string, CvlOption[]>>({});
  const isCvlDataLoading = ref<boolean>(false);

  function isCvlType(argument: CustomFunctionArgument): boolean {
    if (argument?.type === undefined) {
      return false;
    }

    return argument.type.toLowerCase().startsWith('cvl#');
  }

  function getCvlType(argument: CustomFunctionArgument): string {
    if (isCvlType(argument)) {
      // CVL types are in format "CVL#TypeName", so we need to remove "CVL#"
      const cvlPrefix = 'CVL#';
      return argument.type.substring(cvlPrefix.length);
    }

    return '';
  }

  function generateArgumentKey(groupIndex: number, argIndex: number): string {
    return `${groupIndex}-${argIndex}`;
  }

  // Create a computed property that maps arguments to their selected options
  const selectedOptions = computed<Record<string, CvlOption | null>>(() => {
    const result: Record<string, CvlOption | null> = {};

    argumentListRef.value?.argumentGroups?.forEach((group, groupIndex) => {
      group.arguments?.forEach((argument, argIndex) => {
        if (isCvlType(argument)) {
          const cvlType = getCvlType(argument);
          const options = cvlOptionValues.value[cvlType] || [];
          const currentValue = argument.value || argument.defaultValue;
          const key = generateArgumentKey(groupIndex, argIndex);

          if (currentValue) {
            const selectedOption = options.find((option) => option.Key === currentValue);
            result[key] = selectedOption || null;
          } else {
            result[key] = null;
          }
        }
      });
    });

    return result;
  });

  function getSelectedOption(groupIndex: number, argIndex: number): CvlOption | null {
    return selectedOptions.value[generateArgumentKey(groupIndex, argIndex)] || null;
  }

  function updateArgumentValue(argument: CustomFunctionArgument, selectedOption: CvlOption | null): void {
    if (selectedOption) {
      argument.value = selectedOption.Key;
    } else {
      argument.value = undefined;
    }
  }

  async function loadCvlData(argumentList: CustomFunctionArgumentGroups): Promise<void> {
    const allCvlTypes = extractCustomFunctionCvls(argumentList);

    if (allCvlTypes.length > 0) {
      isCvlDataLoading.value = true;

      try {
        // Fetch all CVL data in parallel for better performance
        const cvlPromises = allCvlTypes.map(async (cvlType) => {
          const cvlOptions = await getCvlValuesByCvlId(cvlType);
          return { cvlType, cvlOptions };
        });

        const results = await Promise.all(cvlPromises);

        // Build the options object from results
        const allCvlOptions: Record<string, CvlOption[]> = {};
        results.forEach(({ cvlType, cvlOptions }) => {
          allCvlOptions[cvlType] = cvlOptions;
        });

        cvlOptionValues.value = allCvlOptions;
      } catch (error) {
        console.error('Failed to load CVL data:', error);
        throw error; // Re-throw to allow caller to handle
      } finally {
        isCvlDataLoading.value = false;
      }
    }
  }

  return {
    cvlOptionValues,
    isCvlDataLoading,
    selectedOptions,
    loadCvlData,
    getSelectedOption,
    updateArgumentValue,
    isCvlType,
    getCvlType,
  };
}

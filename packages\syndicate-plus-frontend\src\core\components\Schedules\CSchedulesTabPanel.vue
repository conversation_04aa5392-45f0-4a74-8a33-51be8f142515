<template>
  <c-section>
    <c-schedules-table :active-tab="isActiveTab" :trading-partner-id="tradingPartnerId" />
  </c-section>
</template>

<script setup lang="ts">
import { toRef } from 'vue';
import { CSchedulesTable } from '@core/components/Schedules';

const props = defineProps({
  activeTab: {
    type: Boolean,
    default: true,
  },
  tradingPartnerId: {
    type: String,
    required: true,
  },
});

// Refs
const isActiveTab = toRef(props, 'activeTab');
</script>

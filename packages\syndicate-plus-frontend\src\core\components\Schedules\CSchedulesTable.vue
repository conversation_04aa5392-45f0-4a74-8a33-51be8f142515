<template>
  <teleport v-if="isTeleportEnabled" to="#right-sidebar">
    <c-tile-btn
      v-if="isDeleteButtonVisible"
      icon="mdi-delete-outline"
      :tooltip-left="$t('core.trading_partners.schedules.delete')"
      :icon-size="20"
      @click="deleteSchedule"
    />
  </teleport>
  <q-inner-loading v-if="isInitializing" :showing="isLoading" color="primary">
    <c-spinner color="primary" size="40" />
  </q-inner-loading>

  <q-table
    v-if="currentSchedules?.length"
    v-model:selected="selectedRows"
    flat
    dense
    hide-bottom
    separator="cell"
    binary-state-sort
    class="schedules-table sticky-table-header"
    :pagination="tablePagination"
    :rows="currentSchedules"
    row-key="id"
    :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
    :columns="tableColumns"
    virtual-scroll
    :virtual-scroll-item-size="31"
    :virtual-scroll-sticky-size-start="31"
    :loading="isLoading"
    @row-click="onRowClick"
    @virtual-scroll="onVirtualScroll"
  >
    <template #loading>
      <q-inner-loading showing color="primary" class="inner-loading">
        <c-spinner color="primary" size="40" />
      </q-inner-loading>
    </template>
  </q-table>
  <div v-else-if="!isInitializing && !isLoading">
    <c-no-data
      src="nothing-to-see"
      image-height="195px"
      :title="$t('core.trading_partners.schedules.no_data.no_schedules_title')"
      :text="$t('core.trading_partners.schedules.no_data.no_schedules_message')"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, computed, toRef, nextTick, onBeforeMount } from 'vue';
import { tableColumns } from '@core/components/Schedules';
import { CNoData } from '@components';
import { useSchedulesStore } from '@core/stores';
import { useSingleRowSelect } from '@composables/Common';
import { Schedule } from '@core/interfaces';
import { notify } from '@inriver/inri';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';

const { t } = useI18n();

const props = defineProps({
  activeTab: {
    type: Boolean,
    required: true,
  },
  tradingPartnerId: {
    type: String,
    required: true,
  },
});

const currentSchedulesStore = useSchedulesStore();

// Refs
const isActiveTabSchedules = toRef(props, 'activeTab');
const tablePagination = ref({
  page: 1,
  rowsPerPage: 0,
  sortBy: 'text',
  descending: false,
});

const isTeleportEnabled = ref(false);
const isInitializing = ref(true);
const { isLoading, currentSchedules, isLastPage } = storeToRefs(currentSchedulesStore);
const { deleteScheduleById } = currentSchedulesStore;

// Computed
const syndicationSelected = computed(() => selectedRows.value?.length);
const isDeleteButtonVisible = computed(() => syndicationSelected.value && isActiveTabSchedules.value);

// Composables
const { selectedRows, onRowClick } = useSingleRowSelect<Schedule>();

// Functions
const onVirtualScroll = (index) => {
  // Check if the user has reached the bottom of the table and more data is available
  if (index.index === index.to && !isLastPage.value) {
    // Only load more data if we haven't reached the last page
    setTimeout(async () => {
      await currentSchedulesStore.fetch(props.tradingPartnerId, false);
      nextTick(() => {
        index.ref.refresh();
      });
    }, 50);
  }
};

const deleteSchedule = async () => {
  if (!selectedRows.value?.length) {
    return;
  }

  const result = await deleteScheduleById(selectedRows.value[0].id);
  await currentSchedulesStore.fetch(props.tradingPartnerId, true);

  if (result) {
    notify.success(t('core.trading_partners.schedules.delete_success'), {
      position: 'bottom-right',
    });
    selectedRows.value = [];
  }
};

// Lifecycle methods
onBeforeMount(async () => {
  await currentSchedulesStore.fetch(props.tradingPartnerId, true);
  isInitializing.value = false;
});

onMounted(() => {
  isTeleportEnabled.value = true;
});
</script>
<style scoped lang="scss">
.schedules-table {
  max-height: calc(100vh - 203px);
  margin-bottom: 200px;
}
</style>

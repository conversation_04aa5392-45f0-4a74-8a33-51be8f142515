<template>
  <c-dialog
    :model-value="showDialog"
    class="c-dialog"
    :title="$t('core.manage_trading_partners.duplicate_mapping')"
    :confirm-button-text="$t('syndicate_plus.common.save')"
    :disable-confirm="confirmButtonIsDisabled"
    @cancel="onCancel"
    @confirm="onSave"
  >
    <q-form ref="form" greedy autofocus @submit.prevent="onSave">
      <div class="row q-gutter-lg">
        <div class="col section">
          <q-input
            v-model="mappingName"
            v-bind="$inri.input"
            :label="$t('core.manage_trading_partners.duplicate_mapping_dialog.mapping_name')"
            :rules="[
              $validate.required(),
              (val: string) =>
                val !== props.originalMappingName ||
                $t('core.manage_trading_partners.duplicate_mapping_dialog.name_must_be_different'),
            ]"
            autofocus
          />
        </div>
      </div>
    </q-form>
  </c-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useDialog } from '@inriver/inri';

// Props
interface Props {
  originalMappingName: string;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'handle-save': [mappingName: string];
  'handle-cancel': [];
}>();

// Composables
const { showDialog, cancel, confirmSuccess } = useDialog();

// Refs
const mappingName = ref(props.originalMappingName);
const form = ref();

// Computed
const confirmButtonIsDisabled = computed(() => {
  return !mappingName.value?.trim() || mappingName.value === props.originalMappingName;
});

// Functions
const onSave = async () => {
  const isValid = await form.value?.validate();
  if (!isValid) {
    return;
  }

  confirmSuccess(null);
  emit('handle-save', mappingName.value.trim());
};

const onCancel = () => {
  cancel();
  emit('handle-cancel');
};
</script>

<style lang="scss" scoped>
.section {
  h3 {
    padding-bottom: 20px;
    font-weight: normal;
  }
}
</style>

import { computed, ComputedRef, Ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { notify } from '@inriver/inri';
import { isMapped } from '@core/services/Mappings/utils';
import { checkIfFunctionRequiresConfiguration, checkIfIsCustomFunction } from '@core/Utils';

export enum MappingPanelType {
  Standard = 'standard',
  DSA = 'dsa',
}

export interface ButtonHandlers {
  save: () => Promise<void>;
  cancelEdit: () => void;
  unmapSourceField: () => void;
  unmapFunction: () => void;
  mapAutomatically?: () => void;
  openDefaultLanguageDialog?: () => void;
  importMappingIntoEditor?: () => Promise<void>;
  exportCurrentMapping?: () => Promise<void>;
  openFunctionSettings?: () => void;
  openMapEnumDialog?: () => void;
  addListItem?: () => void;
  removeListItem?: () => void;
}

export interface ButtonVisibility {
  isUnmapSourceFieldButtonVisible: ComputedRef<boolean>;
  isEditFunctionSettingsButtonVisible: ComputedRef<boolean>;
  isUnmapFunctionButtonVisible: ComputedRef<boolean>;
  isMapEnumButtonVisible: ComputedRef<boolean>;
  isAddListItemButtonVisible: ComputedRef<boolean>;
  isRemoveListItemButtonVisible: ComputedRef<boolean>;
  isAutomapButtonVisible: ComputedRef<boolean>;
  isExportMappingButtonVisible: ComputedRef<boolean>;
  isImportMappingButtonVisible: ComputedRef<boolean>;
  isSetDefaultLanguageButtonVisible: ComputedRef<boolean>;
}

export interface ButtonTooltips {
  unmapSourceFieldTooltip: ComputedRef<string>;
}

export interface FieldMappingButtonConfig {
  handlers: ButtonHandlers;
  visibility: ButtonVisibility;
  tooltips: ButtonTooltips;
}

export interface UseFieldMappingButtonsOptions {
  panelType: MappingPanelType;
  selectedRows: Ref<any[]>;
  selectedResourceRows?: Ref<any[]>;
  displayedRows?: ComputedRef<any[]>;
  isDynamicMapping?: ComputedRef<boolean>;
  selectedRowIsObjectType?: ComputedRef<boolean>;
  // Handler functions to be passed from the component
  saveHandler: () => Promise<void>;
  cancelEditHandler: () => void;
  unmapSourceFieldHandler: () => void;
  unmapFunctionHandler: () => void;
  // Optional handlers
  mapAutomaticallyHandler?: () => void;
  openDefaultLanguageDialogHandler?: () => void;
  importMappingHandler?: () => Promise<void>;
  exportMappingHandler?: () => Promise<void>;
  openFunctionSettingsHandler?: () => void;
  openMapEnumDialogHandler?: () => void;
  addListItemHandler?: () => void;
  removeListItemHandler?: () => void;
  // Visibility function for map enum
  isMapEnumButtonVisibleFn?: ComputedRef<boolean>;
}

export const useFieldMappingButtons = (options: UseFieldMappingButtonsOptions): FieldMappingButtonConfig => {
  const { t } = useI18n();
  const isDsaPanel = options.panelType === MappingPanelType.DSA;

  // Button visibility logic
  const isUnmapSourceFieldButtonVisible = computed(() => {
    if (isDsaPanel) {
      // For DSA panel, check if any selected row has inRiverFieldTypeId because inRiverEntityTypeId is not used in DSA
      return options.selectedRows.value?.some((x: any) => !!x.inRiverFieldTypeId) ?? false;
    } else {
      // For standard panel, check inRiverFieldTypeId
      const hasStandardMappedRows = options.selectedRows.value?.some((x: any) => isMapped(x)) ?? false;

      // For resource rows, check if InRiverFieldTypeId exists (note the capital I)
      const hasResourceMappedRows =
        options.selectedResourceRows?.value?.some((x: any) => !!x.InRiverFieldTypeId) ?? false;

      return hasStandardMappedRows || hasResourceMappedRows;
    }
  });

  const isUnmapFunctionButtonVisible = computed(
    () =>
      (options.selectedRows.value?.some((x: any) => !!x.ConverterArgs) ?? false) ||
      (options.selectedResourceRows?.value?.some((x: any) => !!x.ConverterArgs) ?? false)
  );

  const isOneRowSelected = computed(() => options.selectedRows.value?.length === 1);

  const isEditFunctionSettingsButtonVisible = computed(() => {
    if (!isOneRowSelected.value) {
      return false;
    }

    const selectedRow = options.selectedRows.value[0];

    if (!selectedRow?.ConverterArgs) {
      return false;
    }

    let functionConverterArgs;
    try {
      functionConverterArgs = JSON.parse(selectedRow.ConverterArgs as string);
    } catch (e) {
      console.error('Error parsing ConverterArgs:', e);
      return false; // If parsing fails, assume it doesn't require configuration
    }

    const functionName = functionConverterArgs?.transformations[0]?.function?.name as string;
    const isCustomFunction = checkIfIsCustomFunction(functionName);
    return isCustomFunction || checkIfFunctionRequiresConfiguration(functionName);
  });

  const isMapEnumButtonVisible = computed(() => {
    return options.isMapEnumButtonVisibleFn?.value || false;
  });

  const isAddListItemButtonVisible = computed(() => {
    if (isDsaPanel) {
      return false;
    }

    return !!(options.isDynamicMapping?.value && options.selectedRowIsObjectType?.value && isOneRowSelected.value);
  });

  const isRemoveListItemButtonVisible = computed(() => {
    if (isDsaPanel) {
      return false;
    }

    return !!(
      options.isDynamicMapping?.value &&
      options.selectedRowIsObjectType?.value &&
      isOneRowSelected.value &&
      options.displayedRows?.value?.filter((x) => x.listItemOf === options.selectedRows.value[0].FormatField)?.length
    );
  });

  const isAutomapButtonVisible = computed(() => !isDsaPanel);
  const isExportMappingButtonVisible = computed(() => !isDsaPanel);
  const isImportMappingButtonVisible = computed(() => !isDsaPanel);
  const isSetDefaultLanguageButtonVisible = computed(() => !isDsaPanel);

  // Button handlers
  const handlers: ButtonHandlers = {
    save: async () => {
      try {
        await options.saveHandler();
      } catch (error) {
        console.error('Error saving:', error);
        notify.error(t('core.settings.field_mapping.warning'));
      }
    },

    cancelEdit: () => {
      options.cancelEditHandler();
    },

    mapAutomatically: () => {
      if (options.mapAutomaticallyHandler) {
        options.mapAutomaticallyHandler();
      }
    },

    openDefaultLanguageDialog: () => {
      if (options.openDefaultLanguageDialogHandler) {
        options.openDefaultLanguageDialogHandler();
      }
    },

    importMappingIntoEditor: async () => {
      if (options.importMappingHandler) {
        try {
          await options.importMappingHandler();
        } catch (error) {
          console.error('Error importing mapping:', error);
          notify.error(t('core.settings.field_mapping.import_error'));
        }
      }
    },

    exportCurrentMapping: async () => {
      if (options.exportMappingHandler) {
        try {
          await options.exportMappingHandler();
        } catch (error) {
          console.error('Error exporting mapping:', error);
          notify.error(t('core.settings.field_mapping.export_error'));
        }
      }
    },

    unmapSourceField: () => {
      options.unmapSourceFieldHandler();
    },

    openFunctionSettings: () => {
      if (options.openFunctionSettingsHandler) {
        options.openFunctionSettingsHandler();
      }
    },

    unmapFunction: () => {
      options.unmapFunctionHandler();
    },

    openMapEnumDialog: () => {
      if (options.openMapEnumDialogHandler) {
        options.openMapEnumDialogHandler();
      }
    },

    addListItem: () => {
      if (options.addListItemHandler) {
        options.addListItemHandler();
      }
    },

    removeListItem: () => {
      if (options.removeListItemHandler) {
        options.removeListItemHandler();
      }
    },
  };

  const visibility: ButtonVisibility = {
    isUnmapSourceFieldButtonVisible,
    isEditFunctionSettingsButtonVisible,
    isUnmapFunctionButtonVisible,
    isMapEnumButtonVisible,
    isAddListItemButtonVisible,
    isRemoveListItemButtonVisible,
    isAutomapButtonVisible,
    isExportMappingButtonVisible,
    isImportMappingButtonVisible,
    isSetDefaultLanguageButtonVisible,
  };

  // Tooltip logic - determine if we should show "unmap resource" or "unmap source field"
  const unmapSourceFieldTooltip = computed(() => {
    // Check if any resource is selected
    const hasSelectedResource = (options.selectedResourceRows?.value?.length ?? 0) > 0;
    const hasSelectedStandardRow = (options.selectedRows.value?.length ?? 0) > 0;

    // If resource is selected and no standard rows are selected, show "unmap resource"
    if (hasSelectedResource && !hasSelectedStandardRow) {
      return t('core.settings.field_mapping.unmap_resource');
    }

    // Default to "unmap source field"
    return t('core.settings.field_mapping.unmap_source_field');
  });

  const tooltips: ButtonTooltips = {
    unmapSourceFieldTooltip,
  };

  return {
    handlers,
    visibility,
    tooltips,
  };
};

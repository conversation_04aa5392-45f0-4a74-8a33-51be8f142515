import { vi, beforeEach, describe, it, expect } from 'vitest';
import { ref } from 'vue';
import { FieldType, MapState } from '@core/enums';
import { MappingDetailsResponse, MappingFieldResponse } from '@core/interfaces';
import useEditFieldMappingFilter from './useEditFieldMappingFilter';

// Mock the utils module
vi.mock('@core/services/Mappings/utils', () => ({
  isMapped: vi.fn((item) => !!item.mapped),
}));

describe('useEditFieldMappingFilter', () => {
  let mockMapping: MappingDetailsResponse;

  beforeEach(() => {
    vi.clearAllMocks();

    // Create a minimal mock mapping that matches the interface
    mockMapping = {
      MappingId: 1,
      MappingName: 'Test Mapping',
      WorkareaEntityTypeId: 'Product',
      OutputEntityTypeId: 'Product',
      FirstRelatedEntityTypeId: 'Channel',
      FirstLinkEntityTypeId: null,
      SecondRelatedEntityTypeId: 'Item',
      SecondLinkEntityTypeId: null,
      DefaultLanguage: 'en',
      ResourceFields: [],
      FormatId: 1,
      EnableSKU: true,
      DsaMappingId: null,
      MappingModelList: [
        {
          inRiverEntityTypeId: 'Product',
          inRiverFieldTypeId: 'field1',
          inRiverDataType: 'string',
          FormatField: 'Format Field 1',
          FormatFieldId: 'formatField1',
          DefaultValue: '',
          ConverterArgs: null,
          ConverterClass: null,
          ConverterId: null,
          Recommended: false,
          Mandatory: true,
          Unique: false,
          FormatDataType: 'string',
          MinLength: null,
          MaxLength: null,
          Category: 'Category A',
          Enumerations: [],
          EnumerationValues: '',
          CvlCompleteness: false,
          mapped: true,
        } as MappingFieldResponse & { mapped: boolean },
        {
          inRiverEntityTypeId: 'Product',
          inRiverFieldTypeId: 'field2',
          inRiverDataType: 'string',
          FormatField: 'Format Field 2',
          FormatFieldId: 'formatField2',
          DefaultValue: '',
          ConverterArgs: null,
          ConverterClass: null,
          ConverterId: null,
          Recommended: true,
          Mandatory: false,
          Unique: false,
          FormatDataType: 'string',
          MinLength: null,
          MaxLength: null,
          Category: 'Category B',
          Enumerations: [],
          EnumerationValues: '',
          CvlCompleteness: false,
          mapped: false,
        } as MappingFieldResponse & { mapped: boolean },
        {
          inRiverEntityTypeId: 'Product',
          inRiverFieldTypeId: 'field3',
          inRiverDataType: 'string',
          FormatField: 'Format Field 3',
          FormatFieldId: 'formatField3',
          DefaultValue: '',
          ConverterArgs: null,
          ConverterClass: null,
          ConverterId: null,
          Recommended: false,
          Mandatory: false,
          Unique: true,
          FormatDataType: 'string',
          MinLength: null,
          MaxLength: null,
          Category: 'Category A',
          Enumerations: [],
          EnumerationValues: '',
          CvlCompleteness: false,
          mapped: true,
        } as MappingFieldResponse & { mapped: boolean },
        {
          inRiverEntityTypeId: 'Product',
          inRiverFieldTypeId: 'field4',
          inRiverDataType: 'string',
          FormatField: 'Format Field 4',
          FormatFieldId: 'formatField4',
          DefaultValue: '',
          ConverterArgs: null,
          ConverterClass: null,
          ConverterId: null,
          Recommended: true,
          Mandatory: true,
          Unique: false,
          FormatDataType: 'string',
          MinLength: null,
          MaxLength: null,
          Category: 'Category B',
          Enumerations: [],
          EnumerationValues: '',
          CvlCompleteness: false,
          mapped: false,
        } as MappingFieldResponse & { mapped: boolean },
        {
          inRiverEntityTypeId: 'Product',
          inRiverFieldTypeId: 'field5',
          inRiverDataType: 'string',
          FormatField: 'Format Field 5',
          FormatFieldId: 'formatField5',
          DefaultValue: '',
          ConverterArgs: null,
          ConverterClass: null,
          ConverterId: null,
          Recommended: false,
          Mandatory: false,
          Unique: false,
          FormatDataType: 'string',
          MinLength: null,
          MaxLength: null,
          Category: 'Category C',
          Enumerations: [],
          EnumerationValues: '',
          CvlCompleteness: false,
          mapped: true,
        } as MappingFieldResponse & { mapped: boolean },
      ],
    };
  });

  describe('field type filtering with OR logic', () => {
    it('should show items that match ANY of the selected field types', () => {
      const mappingRef = ref(mockMapping);
      const { selectedFieldTypes, displayedRows } = useEditFieldMappingFilter(mappingRef);

      // Select both MANDATORY and RECOMMENDED
      selectedFieldTypes.value = [FieldType.MANDATORY, FieldType.RECOMMENDED];

      const result = displayedRows.value;

      // Should include items that are MANDATORY OR RECOMMENDED (or both)
      expect(result).toHaveLength(3);
      expect(result.map((r) => r.inRiverFieldTypeId)).toEqual(expect.arrayContaining(['field1', 'field2', 'field4']));

      // Verify the logic:
      // Item 1: Mandatory=true, Recommended=false, Unique=false -> INCLUDED (matches MANDATORY)
      // Item 2: Mandatory=false, Recommended=true, Unique=false -> INCLUDED (matches RECOMMENDED)
      // Item 3: Mandatory=false, Recommended=false, Unique=true -> EXCLUDED (doesn't match either)
      // Item 4: Mandatory=true, Recommended=true, Unique=false -> INCLUDED (matches both)
      // Item 5: Mandatory=false, Recommended=false, Unique=false -> EXCLUDED (doesn't match either)
    });

    it('should show items that match MANDATORY only when only MANDATORY is selected', () => {
      const mappingRef = ref(mockMapping);
      const { selectedFieldTypes, displayedRows } = useEditFieldMappingFilter(mappingRef);

      selectedFieldTypes.value = [FieldType.MANDATORY];

      const result = displayedRows.value;

      // Should include only items that are MANDATORY
      expect(result).toHaveLength(2);
      expect(result.map((r) => r.inRiverFieldTypeId)).toEqual(expect.arrayContaining(['field1', 'field4']));
    });

    it('should show items that match RECOMMENDED only when only RECOMMENDED is selected', () => {
      const mappingRef = ref(mockMapping);
      const { selectedFieldTypes, displayedRows } = useEditFieldMappingFilter(mappingRef);

      selectedFieldTypes.value = [FieldType.RECOMMENDED];

      const result = displayedRows.value;

      // Should include only items that are RECOMMENDED
      expect(result).toHaveLength(2);
      expect(result.map((r) => r.inRiverFieldTypeId)).toEqual(expect.arrayContaining(['field2', 'field4']));
    });

    it('should show items that match UNIQUE only when only UNIQUE is selected', () => {
      const mappingRef = ref(mockMapping);
      const { selectedFieldTypes, displayedRows } = useEditFieldMappingFilter(mappingRef);

      selectedFieldTypes.value = [FieldType.UNIQUE];

      const result = displayedRows.value;

      // Should include only items that are UNIQUE
      expect(result).toHaveLength(1);
      expect(result.map((r) => r.inRiverFieldTypeId)).toEqual(['field3']);
    });

    it('should show items that match ALL three field types when all are selected', () => {
      const mappingRef = ref(mockMapping);
      const { selectedFieldTypes, displayedRows } = useEditFieldMappingFilter(mappingRef);

      selectedFieldTypes.value = [FieldType.MANDATORY, FieldType.RECOMMENDED, FieldType.UNIQUE];

      const result = displayedRows.value;

      // Should include items that match ANY of the three field types
      expect(result).toHaveLength(4);
      expect(result.map((r) => r.inRiverFieldTypeId)).toEqual(
        expect.arrayContaining(['field1', 'field2', 'field3', 'field4'])
      );

      // Only item 5 should be excluded as it doesn't match any field type
    });

    it('should show all items when no field types are selected', () => {
      const mappingRef = ref(mockMapping);
      const { selectedFieldTypes, displayedRows } = useEditFieldMappingFilter(mappingRef);

      selectedFieldTypes.value = [];

      const result = displayedRows.value;

      // Should show all items when no field types are selected
      expect(result).toHaveLength(5);
    });

    it('should show all items when selectedFieldTypes is undefined', () => {
      const mappingRef = ref(mockMapping);
      const { selectedFieldTypes, displayedRows } = useEditFieldMappingFilter(mappingRef);

      selectedFieldTypes.value = undefined;

      const result = displayedRows.value;

      // Should show all items when selectedFieldTypes is undefined
      expect(result).toHaveLength(5);
    });
  });

  describe('combined filtering', () => {
    it('should combine field type filtering with category filtering', () => {
      const mappingRef = ref(mockMapping);
      const { selectedFieldTypes, selectedCategory, displayedRows } = useEditFieldMappingFilter(mappingRef);

      // Select Category A and MANDATORY field type
      selectedCategory.value = 'Category A';
      selectedFieldTypes.value = [FieldType.MANDATORY];

      const result = displayedRows.value;

      // Should include only items in Category A that are MANDATORY
      expect(result).toHaveLength(1);
      expect(result[0].inRiverFieldTypeId).toBe('field1');
    });

    it('should combine field type filtering with map state filtering', () => {
      const mappingRef = ref(mockMapping);
      const { selectedFieldTypes, selectedMapState, displayedRows } = useEditFieldMappingFilter(mappingRef);

      // Select MANDATORY field type and MAPPED state
      selectedFieldTypes.value = [FieldType.MANDATORY];
      selectedMapState.value = MapState.MAPPED;

      const result = displayedRows.value;

      // Should include only items that are MANDATORY and MAPPED
      expect(result).toHaveLength(1);
      expect(result[0].inRiverFieldTypeId).toBe('field1');
    });

    it('should combine field type filtering with search', () => {
      const mappingRef = ref(mockMapping);
      const { selectedFieldTypes, searchValue, displayedRows } = useEditFieldMappingFilter(mappingRef);

      // Select MANDATORY field type and search for "field1"
      selectedFieldTypes.value = [FieldType.MANDATORY];
      searchValue.value = 'field1';

      const result = displayedRows.value;

      // Should include only items that are MANDATORY and match the search
      expect(result).toHaveLength(1);
      expect(result[0].inRiverFieldTypeId).toBe('field1');
    });
  });

  describe('other filtering functionality', () => {
    it('should filter by category correctly', () => {
      const mappingRef = ref(mockMapping);
      const { selectedCategory, displayedRows } = useEditFieldMappingFilter(mappingRef);

      selectedCategory.value = 'Category A';

      const result = displayedRows.value;

      expect(result).toHaveLength(2);
      expect(result.every((r) => r.Category === 'Category A')).toBe(true);
    });

    it('should filter by map state correctly', () => {
      const mappingRef = ref(mockMapping);
      const { selectedMapState, displayedRows } = useEditFieldMappingFilter(mappingRef);

      selectedMapState.value = MapState.MAPPED;

      const result = displayedRows.value;

      expect(result).toHaveLength(3);
      expect(result.map((r) => r.inRiverFieldTypeId)).toEqual(expect.arrayContaining(['field1', 'field3', 'field5']));
    });

    it('should filter by search value correctly', () => {
      const mappingRef = ref(mockMapping);
      const { searchValue, displayedRows } = useEditFieldMappingFilter(mappingRef);

      searchValue.value = 'field1';

      const result = displayedRows.value;

      expect(result).toHaveLength(1);
      expect(result[0].inRiverFieldTypeId).toBe('field1');
    });

    it('should return empty array when mapping is undefined', () => {
      const mappingRef = ref(undefined);
      const { displayedRows } = useEditFieldMappingFilter(mappingRef);

      const result = displayedRows.value;

      expect(result).toHaveLength(0);
    });
  });

  describe('computed properties', () => {
    it('should return all unique categories', () => {
      const mappingRef = ref(mockMapping);
      const { allCategories } = useEditFieldMappingFilter(mappingRef);

      const result = allCategories.value;

      expect(result).toHaveLength(3);
      expect(result).toEqual(expect.arrayContaining(['Category A', 'Category B', 'Category C']));
    });

    it('should return all field types', () => {
      const mappingRef = ref(mockMapping);
      const { allFieldTypes } = useEditFieldMappingFilter(mappingRef);

      const result = allFieldTypes.value;

      expect(result).toEqual([FieldType.MANDATORY, FieldType.RECOMMENDED, FieldType.UNIQUE]);
    });

    it('should return all map states', () => {
      const mappingRef = ref(mockMapping);
      const { allMapStates } = useEditFieldMappingFilter(mappingRef);

      const result = allMapStates.value;

      expect(result).toEqual([MapState.MAPPED, MapState.UNMAPPED]);
    });
  });
});

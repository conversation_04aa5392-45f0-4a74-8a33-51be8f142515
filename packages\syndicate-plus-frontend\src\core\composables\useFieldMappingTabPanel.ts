import { computed, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useFieldMappingStore, useCurrentCoreTradingPartnerStore } from '@core/stores';
import { fetchDynamicMappingById } from '@core/services/Mappings/dynamicMappingsApi';
import { toMappingDetails } from '@core/services/Mappings/utils';

export interface MappingOption {
  MappingId: number;
  MappingName: string;
  isDynamic: boolean;
}

export interface NormalizedMapping {
  MappingId: number;
  MappingName: string;
  FormatFileId: number;
  EnableSKU: boolean;
  DsaMappingId: number | null;
}

export function useFieldMappingTabPanel(tradingPartnerName: string) {
  const fieldMappingStore = useFieldMappingStore();
  const currentTradingPartnerStore = useCurrentCoreTradingPartnerStore();

  // Refs
  const isLoadingDynamicMapping = ref(false);
  const isDynamicMappingSelected = ref(false);
  const isInitialized = ref(false); // Track if we've finished initializing

  // Store refs
  const {
    isLoading: isFieldMappingsStoreLoading,
    selectedMapping,
    selectedMappingFields,
  } = storeToRefs(fieldMappingStore);
  const {
    isLoading: isCurrentTradingPartnerStoreLoading,
    currentMappings,
    currentDynamicMappings,
  } = storeToRefs(currentTradingPartnerStore);

  // Computed properties
  const isLoading = computed(
    () =>
      isFieldMappingsStoreLoading.value ||
      isCurrentTradingPartnerStoreLoading.value ||
      isLoadingDynamicMapping.value ||
      !isInitialized.value
  );

  const hasInitialized = computed(() => !isCurrentTradingPartnerStoreLoading.value);

  const allMappings = computed((): MappingOption[] => {
    const staticMappings = currentMappings.value.map((mapping) => ({
      MappingId: mapping.MappingId,
      MappingName: mapping.MappingName,
      isDynamic: false,
    }));

    // Filter dynamic mappings that have an updated date
    // We only show dynamic mappings that have been updated
    const dynamicMappings = currentDynamicMappings.value
      .filter((mapping) => {
        // Only include mappings that have an updatedDate
        return !!mapping.updatedDate && !!mapping.updatedBy;
      })
      .map((mapping) => ({
        MappingId: mapping.id,
        MappingName: mapping.name,
        isDynamic: true,
      }));

    return [...staticMappings, ...dynamicMappings];
  });

  // Functions
  const loadDynamicMappingDetails = async (mappingId: number): Promise<void> => {
    if (isLoadingDynamicMapping.value) {
      return;
    }

    try {
      isLoadingDynamicMapping.value = true;
      const dynamicMappingResponse = await fetchDynamicMappingById(mappingId);

      if (!dynamicMappingResponse || !dynamicMappingResponse.data) {
        selectedMappingFields.value = [];
        return;
      }

      // Parse the dynamic mapping data
      const dynamicMappingDetails = JSON.parse(dynamicMappingResponse.data);

      // Get entity field types cache model using the store method
      const entityFieldTypesCacheModel = await fieldMappingStore.getEntityFieldTypesCacheModel(dynamicMappingDetails);

      // Convert to the same format as static mappings for display
      const mappingDetails = toMappingDetails(dynamicMappingDetails, entityFieldTypesCacheModel);

      selectedMappingFields.value = mappingDetails?.MappingModelList ? mappingDetails.MappingModelList : [];
    } catch (error) {
      selectedMappingFields.value = [];
      throw error; // Re-throw for testing purposes
    } finally {
      isLoadingDynamicMapping.value = false;
    }
  };

  const createNormalizedMapping = (selectedMappingData: MappingOption): NormalizedMapping => {
    if (!selectedMappingData.isDynamic) {
      // For static mappings, find the actual mapping
      const actualMapping = currentMappings.value.find((m) => m.MappingId === selectedMappingData.MappingId);
      if (!actualMapping) {
        throw new Error(`Static mapping with ID ${selectedMappingData.MappingId} not found`);
      }
      return actualMapping;
    } else {
      // For dynamic mappings, create normalized mapping
      const originalDynamicMapping = currentDynamicMappings.value.find((m) => m.id === selectedMappingData.MappingId);
      return {
        MappingId: selectedMappingData.MappingId,
        MappingName: originalDynamicMapping?.name || selectedMappingData.MappingName,
        FormatFileId: Number(originalDynamicMapping?.environmentFormatId) || 0,
        EnableSKU: originalDynamicMapping?.enableSKU || false,
        DsaMappingId: null,
      };
    }
  };

  const handleMappingSelection = async (selectedMappingData: MappingOption | null): Promise<void> => {
    if (!selectedMappingData) {
      return;
    }

    try {
      isDynamicMappingSelected.value = selectedMappingData.isDynamic;
      const normalizedMapping = createNormalizedMapping(selectedMappingData);
      selectedMapping.value = normalizedMapping;

      if (selectedMappingData.isDynamic) {
        await loadDynamicMappingDetails(selectedMappingData.MappingId);
      } else {
        await fieldMappingStore.loadMappingDetails(selectedMappingData.MappingId);
      }
    } catch (error) {
      // Reset state on error
      selectedMapping.value = undefined;
      selectedMappingFields.value = [];
      isDynamicMappingSelected.value = false;
      throw error; // Re-throw for testing purposes
    }
  };

  const autoSelectFirstMapping = async (): Promise<void> => {
    const mappings = allMappings.value;
    if (mappings?.length && !selectedMapping.value) {
      await handleMappingSelection(mappings[0]);
    }
  };
  const initializeStore = async (): Promise<void> => {
    // Mark as not initialized to show loading
    isInitialized.value = false;

    // Reset selected mapping to ensure UI shows loading state properly
    selectedMapping.value = undefined;
    selectedMappingFields.value = [];

    try {
      // Initialize the store with the trading partner
      await currentTradingPartnerStore.init(tradingPartnerName);
    } finally {
      // Mark as initialized after everything is done
      setTimeout(() => {
        isInitialized.value = true;
      }, 300);
    }
  };

  const clearStore = (): void => {
    fieldMappingStore.clearStore();
  };

  // Auto-select first mapping when mappings become available
  watch(
    () => allMappings.value,
    async (newMappings, oldMappings) => {
      // If mappings changed (switching trading partners), reset initialization
      if (oldMappings && oldMappings.length !== newMappings.length) {
        isInitialized.value = false;
      }

      try {
        if (newMappings?.length) {
          if (!selectedMapping.value) {
            await handleMappingSelection(newMappings[0]);
          }
        } else {
          // If there are no mappings, make sure we're not showing any previous mapping data
          selectedMapping.value = undefined;
          selectedMappingFields.value = [];
        }
      } finally {
        // Mark as initialized after processing mappings
        setTimeout(() => {
          isInitialized.value = true;
        }, 300);
      }
    },
    { immediate: true }
  );

  // Get the trading partner ID for the selected dynamic mapping
  const currentTradingPartnerId = computed(() => {
    if (isDynamicMappingSelected.value && selectedMapping.value?.MappingId) {
      const dynamicMapping = currentDynamicMappings.value.find((m) => m.id === selectedMapping.value?.MappingId);
      return dynamicMapping?.tradingPartnerId || tradingPartnerName;
    }
    return tradingPartnerName;
  });

  const selectedDynamicMappingFormatId = computed(() => {
    if (!isDynamicMappingSelected.value || !selectedMapping.value?.MappingId) {
      return null;
    }

    const dynamicMapping = currentDynamicMappings.value.find((m) => m.id === selectedMapping.value?.MappingId);
    return dynamicMapping?.environmentFormatId || null;
  });

  return {
    // State
    isLoadingDynamicMapping,
    isDynamicMappingSelected,
    isInitialized,
    selectedMapping,
    selectedMappingFields,

    // Computed
    isLoading,
    hasInitialized,
    allMappings,
    currentTradingPartnerId,
    selectedDynamicMappingFormatId,

    // Methods
    handleMappingSelection,
    loadDynamicMappingDetails,
    createNormalizedMapping,
    autoSelectFirstMapping,
    initializeStore,
    clearStore,
  };
}

import { ref, computed } from 'vue';
import { describe, expect, it } from 'vitest';

// This file tests the logic of the confirmIsDisabled computed property
// from useRunSyndicationDialog.ts

describe('confirmIsDisabled logic', () => {
  // Helper function that recreates the computed property logic
  const createConfirmIsDisabled = (isReviewDialog, selectedMapping, selectedOutput) => {
    return computed(() => {
      if (isReviewDialog.value) {
        return !selectedMapping.value;
      } else {
        return !selectedMapping.value || !selectedOutput.value;
      }
    });
  };

  describe('when isReviewDialog is false', () => {
    it('returns true when selectedMapping is undefined', () => {
      // Arrange
      const isReviewDialog = ref(false);
      const selectedMapping = ref(undefined);
      const selectedOutput = ref({});

      // Act
      const confirmIsDisabled = createConfirmIsDisabled(isReviewDialog, selectedMapping, selectedOutput);

      // Assert
      expect(confirmIsDisabled.value).toBe(true);
    });

    it('returns true when selectedOutput is undefined', () => {
      // Arrange
      const isReviewDialog = ref(false);
      const selectedMapping = ref({});
      const selectedOutput = ref(undefined);

      // Act
      const confirmIsDisabled = createConfirmIsDisabled(isReviewDialog, selectedMapping, selectedOutput);

      // Assert
      expect(confirmIsDisabled.value).toBe(true);
    });

    it('returns false when both selectedMapping and selectedOutput are defined', () => {
      // Arrange
      const isReviewDialog = ref(false);
      const selectedMapping = ref({});
      const selectedOutput = ref({});

      // Act
      const confirmIsDisabled = createConfirmIsDisabled(isReviewDialog, selectedMapping, selectedOutput);

      // Assert
      expect(confirmIsDisabled.value).toBe(false);
    });
  });

  describe('when isReviewDialog is true', () => {
    it('returns true when selectedMapping is undefined', () => {
      // Arrange
      const isReviewDialog = ref(true);
      const selectedMapping = ref(undefined);
      const selectedOutput = ref(undefined);

      // Act
      const confirmIsDisabled = createConfirmIsDisabled(isReviewDialog, selectedMapping, selectedOutput);

      // Assert
      expect(confirmIsDisabled.value).toBe(true);
    });

    it('returns false when selectedMapping is defined (regardless of selectedOutput)', () => {
      // Arrange
      const isReviewDialog = ref(true);
      const selectedMapping = ref({});
      const selectedOutput = ref(undefined); // Even with undefined output, should be false

      // Act
      const confirmIsDisabled = createConfirmIsDisabled(isReviewDialog, selectedMapping, selectedOutput);

      // Assert
      expect(confirmIsDisabled.value).toBe(false);
    });
  });
});

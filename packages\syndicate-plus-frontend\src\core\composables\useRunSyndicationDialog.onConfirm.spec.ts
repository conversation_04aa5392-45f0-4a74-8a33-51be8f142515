import { describe, expect, it, beforeEach, vi, afterEach } from 'vitest';
import { ref } from 'vue';
import useRunSyndicationDialog from './useRunSyndicationDialog';
import * as syndicationService from '@core/services/syndicationApi';
import {
  Syndication,
  Mapping,
  DynamicMappingResponse,
  ChannelLinkResponse,
  CoreFormatFile,
  DynamicFormatFile,
  JobRequestResult,
  Output,
} from '@core/interfaces';
import { Workarea, Collection } from '@core/interfaces/Workarea';
import {
  CollectionTypes,
  MappingTypes,
  OutputDestinationTypes,
  IdentifierTypes,
  MappingSourceTypes,
  DynamicOutputType,
} from '@core/enums';
import { CoreOutputLinkResponse, DynamicOutputLinkResponse } from '@core/interfaces/Outputs';

// Mock the syndication service
vi.mock('@core/services/syndicationApi', () => ({
  runSyndication: vi.fn(),
}));

describe('useRunSyndicationDialog - onConfirm method', () => {
  // Mock data
  const mockJobResult: JobRequestResult = {
    jobId: 'test-job-123',
    status: 200,
  };

  const mockCoreMapping: Mapping = {
    MappingId: 1,
    MappingName: 'Test Core Mapping',
    FormatFileId: 10,
    EnableSKU: true,
    DsaMappingId: 5,
  };

  const mockDynamicMapping: DynamicMappingResponse = {
    id: 2,
    name: 'Test Dynamic Mapping',
    data: JSON.stringify({ DsaMappingId: 6 }),
    environmentFormatId: '20',
    enableSKU: false,
    createdBy: 'test-user',
    category: 'test',
    createdDate: '2024-01-01',
    updatedBy: 'test-user',
    updatedDate: '2024-01-02',
    tradingPartnerId: 'tp1',
  };

  const mockCoreFormat: CoreFormatFile = {
    Id: 10,
    Name: 'Test Core Format',
    LastModified: '2024-01-02',
    type: 'core',
  };

  const mockDynamicFormat: DynamicFormatFile = {
    Id: 20,
    Name: 'Test Dynamic Format',
    tradingPartnerId: 'tp1',
    categoryId: 'cat1',
    LastModified: '2024-01-02',
    type: 'dynamic',
    links: {
      channels: [],
      workareas: [],
    },
  };

  const mockOutput: Output = {
    ExtensionId: 'ext-123',
    ExtensionDisplayName: 'Test Output',
    OutputFormat: 'json',
  };

  const mockChannelCollection: Collection<ChannelLinkResponse> = {
    id: 'channel-1',
    text: 'Test Channel',
    type: CollectionTypes.CHANNEL,
    metadata: {
      environmentFormatId: 1,
      channelId: 100,
      channelNodeId: 200,
      channelName: 'Test Channel',
      channelNodeName: 'Test Channel Node',
    },
  };

  const mockWorkareaCollection: Collection<Workarea> = {
    id: 'workarea-1',
    text: 'Test Workarea',
    type: CollectionTypes.WORKAREA,
    metadata: {
      id: 'workarea-1',
      text: 'Test Workarea',
      isquery: false,
    },
  };

  const mockCoreMappingCollection: Collection<Mapping> = {
    id: 'mapping-1',
    text: 'Test Core Mapping',
    type: MappingTypes.CORE_MAPPING,
    metadata: mockCoreMapping,
  };

  const mockDynamicMappingCollection: Collection<DynamicMappingResponse> = {
    id: 'mapping-2',
    text: 'Test Dynamic Mapping',
    type: MappingTypes.DYNAMIC_CORE_MAPPING,
    metadata: mockDynamicMapping,
  };

  // Mock refs
  let currentSyndications: any;
  let coreMappings: any;
  let dynamicMappings: any;
  let coreFormats: any;
  let dynamicFormats: any;
  let dynamicAssignedOutputs: any;
  let coreAssignedOutputs: any;
  let isReviewDialog: any;
  let isDsaDialog: any;

  beforeEach(() => {
    vi.clearAllMocks();

    currentSyndications = ref<Syndication[]>([]);
    coreMappings = ref<Mapping[]>([mockCoreMapping]);
    dynamicMappings = ref<DynamicMappingResponse[]>([mockDynamicMapping]);
    coreFormats = ref<CoreFormatFile[]>([mockCoreFormat]);
    dynamicFormats = ref<DynamicFormatFile[]>([mockDynamicFormat]);
    dynamicAssignedOutputs = ref<DynamicOutputLinkResponse[]>([]);
    coreAssignedOutputs = ref<CoreOutputLinkResponse[]>([]);
    isReviewDialog = ref(false);
    isDsaDialog = ref(false);

    vi.mocked(syndicationService.runSyndication).mockResolvedValue(mockJobResult);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Core Mapping - Channel Collection', () => {
    it('should run syndication with core mapping for channel', async () => {
      const { selectedMapping, selectedOutput, onConfirm } = useRunSyndicationDialog(
        currentSyndications,
        coreMappings,
        dynamicMappings,
        coreFormats,
        dynamicFormats,
        mockChannelCollection,
        dynamicAssignedOutputs,
        coreAssignedOutputs,
        isReviewDialog,
        isDsaDialog
      );

      selectedMapping.value = mockCoreMappingCollection;
      selectedOutput.value = mockOutput;

      const result = await onConfirm();

      expect(result).toEqual(mockJobResult);
      expect(syndicationService.runSyndication).toHaveBeenCalledWith({
        Id: 0,
        MappingId: mockCoreMapping.MappingId,
        MappingName: mockCoreMapping.MappingName,
        ExtensionId: mockOutput.ExtensionId,
        ExtensionDisplayName: mockOutput.ExtensionDisplayName,
        IdentifierType: IdentifierTypes.FILE_FORMAT_ID,
        FileFormatId: mockCoreMapping.FormatFileId,
        EnableSKU: mockCoreMapping.EnableSKU,
        ChannelId: mockChannelCollection.metadata.channelId,
        EntityIds: [mockChannelCollection.metadata.channelNodeId],
      });
    });

    it('should handle core mapping with preview mode', async () => {
      const { selectedMapping, selectedOutput, onConfirm } = useRunSyndicationDialog(
        currentSyndications,
        coreMappings,
        dynamicMappings,
        coreFormats,
        dynamicFormats,
        mockChannelCollection,
        dynamicAssignedOutputs,
        coreAssignedOutputs,
        isReviewDialog,
        isDsaDialog
      );

      selectedMapping.value = mockCoreMappingCollection;
      selectedOutput.value = mockOutput;

      const result = await onConfirm(undefined, true);

      expect(result).toEqual(mockJobResult);
      expect(syndicationService.runSyndication).toHaveBeenCalledWith(
        expect.objectContaining({
          IsPreviewEnabled: true,
          RunPreview: true,
        })
      );
    });
  });

  describe('Core Mapping - Workarea Collection', () => {
    it('should run syndication with core mapping for workarea', async () => {
      const existingSyndication: Syndication = {
        Id: 999,
        Name: 'Existing Syndication',
        MappingId: mockCoreMapping.MappingId,
        MappingName: mockCoreMapping.MappingName,
        ExtensionId: mockOutput.ExtensionId,
        ExtensionDisplayName: mockOutput.ExtensionDisplayName,
        WorkareaId: mockWorkareaCollection.metadata.id,
        IsPreviewEnabled: false,
        RunPreview: false,
        EnableSKU: true,
        RunDsaSyndication: false,
      };

      currentSyndications.value = [existingSyndication];

      const { selectedMapping, selectedOutput, onConfirm } = useRunSyndicationDialog(
        currentSyndications,
        coreMappings,
        dynamicMappings,
        coreFormats,
        dynamicFormats,
        mockWorkareaCollection,
        dynamicAssignedOutputs,
        coreAssignedOutputs,
        isReviewDialog,
        isDsaDialog
      );

      selectedMapping.value = mockCoreMappingCollection;
      selectedOutput.value = mockOutput;

      const result = await onConfirm();

      expect(result).toEqual(mockJobResult);
      expect(syndicationService.runSyndication).toHaveBeenCalledWith(
        expect.objectContaining({
          Id: existingSyndication.Id,
          Name: existingSyndication.Name,
          WorkareaId: mockWorkareaCollection.metadata.id,
          WorkareaName: mockWorkareaCollection.metadata.text,
        })
      );
    });

    it('should run syndication with selected entity IDs', async () => {
      const { selectedMapping, selectedOutput, onConfirm } = useRunSyndicationDialog(
        currentSyndications,
        coreMappings,
        dynamicMappings,
        coreFormats,
        dynamicFormats,
        mockWorkareaCollection,
        dynamicAssignedOutputs,
        coreAssignedOutputs,
        isReviewDialog,
        isDsaDialog
      );

      selectedMapping.value = mockCoreMappingCollection;
      selectedOutput.value = mockOutput;

      const selectedEntityIds = [1, 2, 3];
      const result = await onConfirm(selectedEntityIds);

      expect(result).toEqual(mockJobResult);
      expect(syndicationService.runSyndication).toHaveBeenCalledWith(
        expect.objectContaining({
          WorkareaId: '',
          WorkareaName: '',
          EntityIds: selectedEntityIds,
        })
      );
    });
  });

  describe('Dynamic Mapping - Channel Collection', () => {
    it('should run syndication with dynamic mapping for channel', async () => {
      const { selectedMapping, selectedOutput, onConfirm } = useRunSyndicationDialog(
        currentSyndications,
        coreMappings,
        dynamicMappings,
        coreFormats,
        dynamicFormats,
        mockChannelCollection,
        dynamicAssignedOutputs,
        coreAssignedOutputs,
        isReviewDialog,
        isDsaDialog
      );

      selectedMapping.value = mockDynamicMappingCollection;
      selectedOutput.value = mockOutput;

      const result = await onConfirm();

      expect(result).toEqual(mockJobResult);
      expect(syndicationService.runSyndication).toHaveBeenCalledWith({
        Id: 0,
        ExtensionDisplayName: mockOutput.ExtensionDisplayName,
        ExtensionId: mockOutput.ExtensionId,
        OutputDestination: OutputDestinationTypes.EXTENSION,
        MappingSource: MappingSourceTypes.OUTPUT_ADAPTER,
        MappingId: mockDynamicMapping.id,
        MappingName: mockDynamicMapping.name,
        DynamicFormatId: Number(mockDynamicMapping.environmentFormatId),
        IdentifierType: IdentifierTypes.DYNAMIC_FORMAT_ID,
        EnableSKU: mockDynamicMapping.enableSKU,
        ChannelId: mockChannelCollection.metadata.channelId,
        EntityIds: [mockChannelCollection.metadata.channelNodeId],
      });
    });

    it('should set OUTPUT_ADAPTER destination for API output type', async () => {
      const apiOutput: Output = {
        ExtensionId: DynamicOutputType.API,
        ExtensionDisplayName: DynamicOutputType.API,
        OutputFormat: 'api',
      };

      const { selectedMapping, selectedOutput, onConfirm } = useRunSyndicationDialog(
        currentSyndications,
        coreMappings,
        dynamicMappings,
        coreFormats,
        dynamicFormats,
        mockChannelCollection,
        dynamicAssignedOutputs,
        coreAssignedOutputs,
        isReviewDialog,
        isDsaDialog
      );

      selectedMapping.value = mockDynamicMappingCollection;
      selectedOutput.value = apiOutput;

      const result = await onConfirm();

      expect(result).toEqual(mockJobResult);
      expect(syndicationService.runSyndication).toHaveBeenCalledWith(
        expect.objectContaining({
          OutputDestination: OutputDestinationTypes.OUTPUT_ADAPTER,
        })
      );
    });
  });

  describe('DSA Dialog Mode', () => {
    beforeEach(() => {
      isDsaDialog.value = true;
    });

    it('should handle DSA with core mapping', async () => {
      const { selectedMapping, selectedOutput, onConfirm } = useRunSyndicationDialog(
        currentSyndications,
        coreMappings,
        dynamicMappings,
        coreFormats,
        dynamicFormats,
        mockChannelCollection,
        dynamicAssignedOutputs,
        coreAssignedOutputs,
        isReviewDialog,
        isDsaDialog
      );

      selectedMapping.value = mockCoreMappingCollection;
      selectedOutput.value = mockOutput;

      const result = await onConfirm();

      expect(result).toEqual(mockJobResult);
      expect(syndicationService.runSyndication).toHaveBeenCalledWith(
        expect.objectContaining({
          DsaMappingId: mockCoreMapping.DsaMappingId,
          RunDsaSyndication: true,
          OutputDestination: OutputDestinationTypes.OUTPUT_ADAPTER,
        })
      );
    });

    it('should handle DSA with dynamic mapping', async () => {
      const { selectedMapping, selectedOutput, onConfirm } = useRunSyndicationDialog(
        currentSyndications,
        coreMappings,
        dynamicMappings,
        coreFormats,
        dynamicFormats,
        mockChannelCollection,
        dynamicAssignedOutputs,
        coreAssignedOutputs,
        isReviewDialog,
        isDsaDialog
      );

      selectedMapping.value = mockDynamicMappingCollection;
      selectedOutput.value = mockOutput;

      const result = await onConfirm();

      expect(result).toEqual(mockJobResult);
      expect(syndicationService.runSyndication).toHaveBeenCalledWith(
        expect.objectContaining({
          DsaMappingId: 6, // Parsed from mockDynamicMapping.data
          RunDsaSyndication: true,
          OutputDestination: OutputDestinationTypes.OUTPUT_ADAPTER,
        })
      );
    });

    it('should handle DSA with null DsaMappingId in dynamic mapping', async () => {
      const dynamicMappingWithoutDsa = {
        ...mockDynamicMapping,
        data: JSON.stringify({ someOtherField: 'value' }),
      };

      const dynamicMappingCollectionWithoutDsa: Collection<DynamicMappingResponse> = {
        id: 'mapping-without-dsa',
        text: 'Dynamic Mapping without DSA',
        type: MappingTypes.DYNAMIC_CORE_MAPPING,
        metadata: dynamicMappingWithoutDsa,
      };

      const { selectedMapping, selectedOutput, onConfirm } = useRunSyndicationDialog(
        currentSyndications,
        coreMappings,
        dynamicMappings,
        coreFormats,
        dynamicFormats,
        mockChannelCollection,
        dynamicAssignedOutputs,
        coreAssignedOutputs,
        isReviewDialog,
        isDsaDialog
      );

      selectedMapping.value = dynamicMappingCollectionWithoutDsa;
      selectedOutput.value = mockOutput;

      const result = await onConfirm();

      expect(result).toEqual(mockJobResult);
      expect(syndicationService.runSyndication).toHaveBeenCalledWith(
        expect.objectContaining({
          DsaMappingId: undefined,
        })
      );

      // Should not set RunDsaSyndication and OutputDestination when DsaMappingId is null
      const calledArgs = vi.mocked(syndicationService.runSyndication).mock.calls[0][0];
      expect(calledArgs.RunDsaSyndication).toBeUndefined();
      expect(calledArgs.OutputDestination).not.toBe(OutputDestinationTypes.OUTPUT_ADAPTER);
    });

    it('should handle DSA with undefined DsaMappingId in core mapping', async () => {
      const coreMappingWithoutDsa = {
        ...mockCoreMapping,
        DsaMappingId: null,
      };

      const coreMappingCollectionWithoutDsa: Collection<Mapping> = {
        id: 'core-mapping-without-dsa',
        text: 'Core Mapping without DSA',
        type: MappingTypes.CORE_MAPPING,
        metadata: coreMappingWithoutDsa,
      };

      const { selectedMapping, selectedOutput, onConfirm } = useRunSyndicationDialog(
        currentSyndications,
        coreMappings,
        dynamicMappings,
        coreFormats,
        dynamicFormats,
        mockChannelCollection,
        dynamicAssignedOutputs,
        coreAssignedOutputs,
        isReviewDialog,
        isDsaDialog
      );

      selectedMapping.value = coreMappingCollectionWithoutDsa;
      selectedOutput.value = mockOutput;

      const result = await onConfirm();

      expect(result).toEqual(mockJobResult);
      expect(syndicationService.runSyndication).toHaveBeenCalledWith(
        expect.objectContaining({
          DsaMappingId: undefined,
        })
      );

      // Should not set RunDsaSyndication and OutputDestination when DsaMappingId is falsy
      const calledArgs = vi.mocked(syndicationService.runSyndication).mock.calls[0][0];
      expect(calledArgs.RunDsaSyndication).toBeUndefined();
      expect(calledArgs.OutputDestination).not.toBe(OutputDestinationTypes.OUTPUT_ADAPTER);
    });
  });

  describe('Error Handling', () => {
    it('should return null when no mapping is selected', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {
        // Intentionally empty
      });

      const { onConfirm } = useRunSyndicationDialog(
        currentSyndications,
        coreMappings,
        dynamicMappings,
        coreFormats,
        dynamicFormats,
        mockChannelCollection,
        dynamicAssignedOutputs,
        coreAssignedOutputs,
        isReviewDialog,
        isDsaDialog
      );

      const result = await onConfirm();

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith('Selected mapping is not defined');
      expect(syndicationService.runSyndication).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should handle service errors gracefully', async () => {
      const error = new Error('Service error');
      vi.mocked(syndicationService.runSyndication).mockRejectedValue(error);

      const { selectedMapping, selectedOutput, onConfirm, isLoading } = useRunSyndicationDialog(
        currentSyndications,
        coreMappings,
        dynamicMappings,
        coreFormats,
        dynamicFormats,
        mockChannelCollection,
        dynamicAssignedOutputs,
        coreAssignedOutputs,
        isReviewDialog,
        isDsaDialog
      );

      selectedMapping.value = mockCoreMappingCollection;
      selectedOutput.value = mockOutput;

      await expect(onConfirm()).rejects.toThrow('Service error');

      // isLoading should be reset to false even when error occurs
      expect(isLoading.value).toBe(false);
    });
  });

  describe('Loading State', () => {
    it('should manage loading state during onConfirm execution', async () => {
      let resolvePromise: ((value: JobRequestResult) => void) | undefined;
      const servicePromise = new Promise<JobRequestResult>((resolve) => {
        resolvePromise = resolve;
      });

      vi.mocked(syndicationService.runSyndication).mockReturnValue(servicePromise);

      const { selectedMapping, selectedOutput, onConfirm, isLoading } = useRunSyndicationDialog(
        currentSyndications,
        coreMappings,
        dynamicMappings,
        coreFormats,
        dynamicFormats,
        mockChannelCollection,
        dynamicAssignedOutputs,
        coreAssignedOutputs,
        isReviewDialog,
        isDsaDialog
      );

      selectedMapping.value = mockCoreMappingCollection;
      selectedOutput.value = mockOutput;

      expect(isLoading.value).toBe(false);

      const confirmPromise = onConfirm();

      // Should be loading during execution
      expect(isLoading.value).toBe(true);

      if (resolvePromise) {
        resolvePromise(mockJobResult);
      }
      await confirmPromise;

      // Should not be loading after completion
      expect(isLoading.value).toBe(false);
    });
  });

  describe('JSON Parsing Edge Cases', () => {
    it('should handle invalid JSON in dynamic mapping data gracefully', async () => {
      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {
        // Intentionally empty
      });

      const dynamicMappingWithInvalidJson = {
        ...mockDynamicMapping,
        data: 'invalid-json',
      };

      const dynamicMappingCollectionWithInvalidJson: Collection<DynamicMappingResponse> = {
        id: 'mapping-invalid-json',
        text: 'Dynamic Mapping with Invalid JSON',
        type: MappingTypes.DYNAMIC_CORE_MAPPING,
        metadata: dynamicMappingWithInvalidJson,
      };

      isDsaDialog.value = true;

      const { selectedMapping, selectedOutput, onConfirm } = useRunSyndicationDialog(
        currentSyndications,
        coreMappings,
        dynamicMappings,
        coreFormats,
        dynamicFormats,
        mockChannelCollection,
        dynamicAssignedOutputs,
        coreAssignedOutputs,
        isReviewDialog,
        isDsaDialog
      );

      selectedMapping.value = dynamicMappingCollectionWithInvalidJson;
      selectedOutput.value = mockOutput;

      const result = await onConfirm();

      // Should handle gracefully and complete successfully
      expect(result).toEqual(mockJobResult);

      // Should log a warning about invalid JSON
      expect(consoleWarnSpy).toHaveBeenCalledWith('Failed to parse dynamic mapping data JSON:', expect.any(Error));

      // Should set DsaMappingId to undefined when JSON parsing fails
      expect(syndicationService.runSyndication).toHaveBeenCalledWith(
        expect.objectContaining({
          DsaMappingId: undefined,
        })
      );

      // Should not set RunDsaSyndication and OutputDestination when DsaMappingId is undefined
      const calledArgs = vi.mocked(syndicationService.runSyndication).mock.calls[0][0];
      expect(calledArgs.RunDsaSyndication).toBeUndefined();
      expect(calledArgs.OutputDestination).not.toBe(OutputDestinationTypes.OUTPUT_ADAPTER);

      consoleWarnSpy.mockRestore();
    });
  });
});

import { describe, it, expect, vi, beforeEach, afterAll } from 'vitest';
import { getMappingsWithDsa } from './useRunSyndicationDialogHelper';
import { MappingTypes } from '@core/enums';
import { Collection } from '@core/interfaces/Workarea';
import { Mapping, DynamicMappingResponse } from '@core/interfaces';

// Mock console.error to avoid noise in test output
const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => undefined);

describe('getMappingsWithDsa', () => {
  beforeEach(() => {
    consoleSpy.mockClear();
  });

  afterAll(() => {
    consoleSpy.mockRestore();
  });

  describe('Core mappings', () => {
    it('should return core mappings with DsaMappingId', () => {
      const mappings: Collection<Mapping | DynamicMappingResponse>[] = [
        {
          id: '1',
          text: 'Core Mapping 1',
          type: MappingTypes.CORE_MAPPING,
          metadata: {
            MappingId: 1,
            MappingName: 'Core Mapping 1',
            FormatFileId: 1,
            EnableSKU: true,
            DsaMappingId: 100,
          } as Mapping,
        },
        {
          id: '2',
          text: 'Core Mapping 2',
          type: MappingTypes.CORE_MAPPING,
          metadata: {
            MappingId: 2,
            MappingName: 'Core Mapping 2',
            FormatFileId: 2,
            EnableSKU: true,
            DsaMappingId: null,
            // No DsaMappingId
          } as Mapping,
        },
      ];

      const result = getMappingsWithDsa(mappings);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('1');
      expect((result[0].metadata as Mapping).DsaMappingId).toBe(100);
    });

    it('should exclude core mappings without DsaMappingId', () => {
      const mappings: Collection<Mapping | DynamicMappingResponse>[] = [
        {
          id: '1',
          text: 'Core Mapping 1',
          type: MappingTypes.CORE_MAPPING,
          metadata: {
            MappingId: 1,
            MappingName: 'Core Mapping 1',
            FormatFileId: 1,
            // No DsaMappingId
          } as Mapping,
        },
      ];

      const result = getMappingsWithDsa(mappings);

      expect(result).toHaveLength(0);
    });

    it('should exclude core mappings with falsy DsaMappingId', () => {
      const mappings: Collection<Mapping | DynamicMappingResponse>[] = [
        {
          id: '1',
          text: 'Core Mapping 1',
          type: MappingTypes.CORE_MAPPING,
          metadata: {
            MappingId: 1,
            MappingName: 'Core Mapping 1',
            FormatFileId: 1,
            DsaMappingId: 0,
          } as Mapping,
        },
        {
          id: '2',
          text: 'Core Mapping 2',
          type: MappingTypes.CORE_MAPPING,
          metadata: {
            MappingId: 2,
            MappingName: 'Core Mapping 2',
            FormatFileId: 2,
            DsaMappingId: null,
          } as Mapping,
        },
        {
          id: '3',
          text: 'Core Mapping 3',
          type: MappingTypes.CORE_MAPPING,
          metadata: {
            MappingId: 3,
            MappingName: 'Core Mapping 3',
            FormatFileId: 3,
            DsaMappingId: null,
          } as Mapping,
        },
      ];

      const result = getMappingsWithDsa(mappings);

      expect(result).toHaveLength(0);
    });
  });

  describe('Dynamic core mappings', () => {
    it('should return dynamic mappings with DsaMappingId in JSON data', () => {
      const mappings: Collection<Mapping | DynamicMappingResponse>[] = [
        {
          id: '1',
          text: 'Dynamic Mapping 1',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 1,
            name: 'Dynamic Mapping 1',
            data: JSON.stringify({ DsaMappingId: 200, otherField: 'value' }),
            environmentFormatId: '1',
          } as DynamicMappingResponse,
        },
        {
          id: '2',
          text: 'Dynamic Mapping 2',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 2,
            name: 'Dynamic Mapping 2',
            data: JSON.stringify({ otherField: 'value' }),
            environmentFormatId: '2',
          } as DynamicMappingResponse,
        },
      ];

      const result = getMappingsWithDsa(mappings);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('1');
      expect((result[0].metadata as DynamicMappingResponse).id).toBe(1);
    });

    it('should exclude dynamic mappings without DsaMappingId in JSON data', () => {
      const mappings: Collection<Mapping | DynamicMappingResponse>[] = [
        {
          id: '1',
          text: 'Dynamic Mapping 1',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 1,
            name: 'Dynamic Mapping 1',
            data: JSON.stringify({ otherField: 'value' }),
            environmentFormatId: '1',
          } as DynamicMappingResponse,
        },
      ];

      const result = getMappingsWithDsa(mappings);

      expect(result).toHaveLength(0);
    });

    it('should exclude dynamic mappings with falsy DsaMappingId in JSON data', () => {
      const mappings: Collection<Mapping | DynamicMappingResponse>[] = [
        {
          id: '1',
          text: 'Dynamic Mapping 1',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 1,
            name: 'Dynamic Mapping 1',
            data: JSON.stringify({ DsaMappingId: 0 }),
            environmentFormatId: '1',
          } as DynamicMappingResponse,
        },
        {
          id: '2',
          text: 'Dynamic Mapping 2',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 2,
            name: 'Dynamic Mapping 2',
            data: JSON.stringify({ DsaMappingId: null }),
            environmentFormatId: '2',
          } as DynamicMappingResponse,
        },
        {
          id: '3',
          text: 'Dynamic Mapping 3',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 3,
            name: 'Dynamic Mapping 3',
            data: JSON.stringify({ DsaMappingId: undefined }),
            environmentFormatId: '3',
          } as DynamicMappingResponse,
        },
      ];

      const result = getMappingsWithDsa(mappings);

      expect(result).toHaveLength(0);
    });

    it('should handle invalid JSON in dynamic mapping data gracefully', () => {
      const mappings: Collection<Mapping | DynamicMappingResponse>[] = [
        {
          id: '1',
          text: 'Dynamic Mapping 1',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 1,
            name: 'Dynamic Mapping 1',
            data: 'invalid json {',
            environmentFormatId: '1',
          } as DynamicMappingResponse,
        },
        {
          id: '2',
          text: 'Dynamic Mapping 2',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 2,
            name: 'Dynamic Mapping 2',
            data: JSON.stringify({ DsaMappingId: 300 }),
            environmentFormatId: '2',
          } as DynamicMappingResponse,
        },
      ];

      const result = getMappingsWithDsa(mappings);

      // Should exclude the mapping with invalid JSON but include the valid one
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('2');
      expect((result[0].metadata as DynamicMappingResponse).id).toBe(2);
    });

    it('should handle empty JSON data gracefully', () => {
      const mappings: Collection<Mapping | DynamicMappingResponse>[] = [
        {
          id: '1',
          text: 'Dynamic Mapping 1',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 1,
            name: 'Dynamic Mapping 1',
            data: '',
            environmentFormatId: '1',
          } as DynamicMappingResponse,
        },
        {
          id: '2',
          text: 'Dynamic Mapping 2',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 2,
            name: 'Dynamic Mapping 2',
            data: '{}',
            environmentFormatId: '2',
          } as DynamicMappingResponse,
        },
      ];

      const result = getMappingsWithDsa(mappings);

      expect(result).toHaveLength(0);
    });

    it('should handle null JSON parse result gracefully', () => {
      const mappings: Collection<Mapping | DynamicMappingResponse>[] = [
        {
          id: '1',
          text: 'Dynamic Mapping 1',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 1,
            name: 'Dynamic Mapping 1',
            data: 'null',
            environmentFormatId: '1',
          } as DynamicMappingResponse,
        },
      ];

      const result = getMappingsWithDsa(mappings);

      expect(result).toHaveLength(0);
    });
  });

  describe('Mixed mapping types', () => {
    it('should handle mixed core and dynamic mappings correctly', () => {
      const mappings: Collection<Mapping | DynamicMappingResponse>[] = [
        {
          id: '1',
          text: 'Core Mapping 1',
          type: MappingTypes.CORE_MAPPING,
          metadata: {
            MappingId: 1,
            MappingName: 'Core Mapping 1',
            FormatFileId: 1,
            DsaMappingId: 100,
          } as Mapping,
        },
        {
          id: '2',
          text: 'Dynamic Mapping 1',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 2,
            name: 'Dynamic Mapping 1',
            data: JSON.stringify({ DsaMappingId: 200 }),
            environmentFormatId: '1',
          } as DynamicMappingResponse,
        },
        {
          id: '3',
          text: 'Core Mapping 2',
          type: MappingTypes.CORE_MAPPING,
          metadata: {
            MappingId: 3,
            MappingName: 'Core Mapping 2',
            FormatFileId: 2,
            // No DsaMappingId
          } as Mapping,
        },
        {
          id: '4',
          text: 'Dynamic Mapping 2',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 4,
            name: 'Dynamic Mapping 2',
            data: JSON.stringify({ otherField: 'value' }),
            environmentFormatId: '2',
          } as DynamicMappingResponse,
        },
      ];

      const result = getMappingsWithDsa(mappings);

      expect(result).toHaveLength(2);
      expect(result.find((m) => m.id === '1')).toBeDefined();
      expect(result.find((m) => m.id === '2')).toBeDefined();
      expect(result.find((m) => m.id === '3')).toBeUndefined();
      expect(result.find((m) => m.id === '4')).toBeUndefined();
    });
  });

  describe('Edge cases', () => {
    it('should return empty array for empty input', () => {
      const result = getMappingsWithDsa([]);

      expect(result).toEqual([]);
    });

    it('should handle mappings with unknown types gracefully', () => {
      const mappings: Collection<Mapping | DynamicMappingResponse>[] = [
        {
          id: '1',
          text: 'Unknown Mapping',
          type: 'UNKNOWN_TYPE' as any,
          metadata: {} as any,
        },
        {
          id: '2',
          text: 'Core Mapping',
          type: MappingTypes.CORE_MAPPING,
          metadata: {
            MappingId: 1,
            MappingName: 'Core Mapping',
            FormatFileId: 1,
            DsaMappingId: 100,
          } as Mapping,
        },
      ];

      const result = getMappingsWithDsa(mappings);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('2');
    });

    it('should handle complex JSON structures in dynamic mapping data', () => {
      const mappings: Collection<Mapping | DynamicMappingResponse>[] = [
        {
          id: '1',
          text: 'Dynamic Mapping 1',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 1,
            name: 'Dynamic Mapping 1',
            data: JSON.stringify({
              DsaMappingId: 400,
              nested: {
                field: 'value',
                array: [1, 2, 3],
              },
              settings: {
                enabled: true,
                config: { timeout: 5000 },
              },
            }),
            environmentFormatId: '1',
          } as DynamicMappingResponse,
        },
      ];

      const result = getMappingsWithDsa(mappings);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('1');
    });

    it('should handle numeric string DsaMappingId values', () => {
      const mappings: Collection<Mapping | DynamicMappingResponse>[] = [
        {
          id: '1',
          text: 'Dynamic Mapping 1',
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
          metadata: {
            id: 1,
            name: 'Dynamic Mapping 1',
            data: JSON.stringify({ DsaMappingId: '500' }),
            environmentFormatId: '1',
          } as DynamicMappingResponse,
        },
      ];

      const result = getMappingsWithDsa(mappings);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('1');
    });
  });
});

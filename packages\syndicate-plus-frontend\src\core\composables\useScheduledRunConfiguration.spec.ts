import { describe, it, expect, beforeEach } from 'vitest';
import { nextTick } from 'vue';
import { useScheduledRunConfiguration } from './useScheduledRunConfiguration';
import { Frequency, Day } from '@enums/ApiSyndication';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

describe('useScheduledRunConfiguration', () => {
  let composable: ReturnType<typeof useScheduledRunConfiguration>;

  beforeEach(() => {
    composable = useScheduledRunConfiguration();
    composable.scheduleName.value = 'Test Schedule';
    composable.selectedDays.value = [];
    composable.selectedFrequency.value = undefined;
    composable.currentInputValue.value = '';
  });

  it('should be invalid if schedule name is missing', () => {
    composable.scheduleName.value = '';
    expect(composable.isInvalid.value).toBe(true);
  });

  it('should be invalid if frequency is not selected', () => {
    expect(composable.isInvalid.value).toBe(true);
  });

  it('should be invalid for weekly if no days are selected', async () => {
    // First set the frequency and wait for the watcher to settle
    composable.selectedFrequency.value = Frequency.WEEKLY;
    await nextTick();
    await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher timeout

    // Set a future time - use a larger offset to ensure it stays in the future
    const futureTime = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes in future
    const formatted = composable.dateTimeMask
      .replace('YYYY', String(futureTime.getFullYear()))
      .replace('MM', String(futureTime.getMonth() + 1).padStart(2, '0'))
      .replace('DD', String(futureTime.getDate()).padStart(2, '0'))
      .replace('HH', String(futureTime.getHours()).padStart(2, '0'))
      .replace('mm', String(futureTime.getMinutes()).padStart(2, '0'));
    composable.startDate.value = formatted;
    await nextTick();
    expect(composable.isInvalid.value).toBe(true);
    composable.selectedDays.value = [Day.MONDAY];
    await nextTick();
    expect(composable.isInvalid.value).toBe(false);
  });

  it('should be valid for ONCE with future date', async () => {
    // First set the frequency and wait for the watcher to settle
    composable.selectedFrequency.value = Frequency.ONCE;
    await nextTick();
    await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher timeout

    const futureDate = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes in future
    const formatted = composable.dateTimeMask
      .replace('YYYY', String(futureDate.getFullYear()))
      .replace('MM', String(futureDate.getMonth() + 1).padStart(2, '0'))
      .replace('DD', String(futureDate.getDate()).padStart(2, '0'))
      .replace('HH', String(futureDate.getHours()).padStart(2, '0'))
      .replace('mm', String(futureDate.getMinutes()).padStart(2, '0'));
    // Set startDate directly to bypass input validation in tests
    composable.startDate.value = formatted;
    await nextTick();
    expect(composable.isInvalid.value).toBe(false);
  });

  it('should be invalid for ONCE with past date', async () => {
    // First set the frequency and wait for the watcher to settle
    composable.selectedFrequency.value = Frequency.ONCE;
    await nextTick();
    await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher timeout

    const pastDate = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes in past
    const formatted = composable.dateTimeMask
      .replace('YYYY', String(pastDate.getFullYear()))
      .replace('MM', String(pastDate.getMonth() + 1).padStart(2, '0'))
      .replace('DD', String(pastDate.getDate()).padStart(2, '0'))
      .replace('HH', String(pastDate.getHours()).padStart(2, '0'))
      .replace('mm', String(pastDate.getMinutes()).padStart(2, '0'));
    composable.startDate.value = formatted;
    await nextTick();
    expect(composable.isInvalid.value).toBe(true);
  });

  it('should be valid for DAILY with valid time', async () => {
    // First set the frequency and wait for the watcher to settle
    composable.selectedFrequency.value = Frequency.DAILY;
    await nextTick();
    await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher timeout

    // Set a future time for today
    const futureTime = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes in future
    const formatted = composable.dateTimeMask
      .replace('YYYY', String(futureTime.getFullYear()))
      .replace('MM', String(futureTime.getMonth() + 1).padStart(2, '0'))
      .replace('DD', String(futureTime.getDate()).padStart(2, '0'))
      .replace('HH', String(futureTime.getHours()).padStart(2, '0'))
      .replace('mm', String(futureTime.getMinutes()).padStart(2, '0'));
    composable.startDate.value = formatted;
    await nextTick();
    expect(composable.isInvalid.value).toBe(false);
  });

  it('should be valid for DAILY with past time', async () => {
    // First set the frequency and wait for the watcher to settle
    composable.selectedFrequency.value = Frequency.DAILY;
    await nextTick();
    await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher timeout

    // Set a past time for today - this should be valid for DAILY schedules
    const pastTime = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes in past
    const formatted = composable.dateTimeMask
      .replace('YYYY', String(pastTime.getFullYear()))
      .replace('MM', String(pastTime.getMonth() + 1).padStart(2, '0'))
      .replace('DD', String(pastTime.getDate()).padStart(2, '0'))
      .replace('HH', String(pastTime.getHours()).padStart(2, '0'))
      .replace('mm', String(pastTime.getMinutes()).padStart(2, '0'));
    composable.startDate.value = formatted;
    await nextTick();
    expect(composable.isInvalid.value).toBe(false);
  });

  it('should build correct configuration object', async () => {
    composable.scheduleName.value = 'My Schedule';
    // First set the frequency and wait for the watcher to settle
    composable.selectedFrequency.value = Frequency.ONCE;
    await nextTick();
    await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher timeout

    const futureDate = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes in future
    const formatted = composable.dateTimeMask
      .replace('YYYY', String(futureDate.getFullYear()))
      .replace('MM', String(futureDate.getMonth() + 1).padStart(2, '0'))
      .replace('DD', String(futureDate.getDate()).padStart(2, '0'))
      .replace('HH', String(futureDate.getHours()).padStart(2, '0'))
      .replace('mm', String(futureDate.getMinutes()).padStart(2, '0'));
    composable.startDate.value = formatted;
    await nextTick();
    const config = composable.getConfiguration();
    expect(config).toBeDefined();
    expect(config?.name).toBe('My Schedule');
    expect(config?.endDate).toBeNull();
    expect(config?.cronExpression).toBeDefined();
    expect(config?.startDate).toBeDefined();
  });

  it('should be valid for MONTHLY with future date', async () => {
    composable.selectedFrequency.value = Frequency.MONTHLY;
    await nextTick();
    await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher timeout

    const futureDate = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes in future
    const formatted = composable.dateTimeMask
      .replace('YYYY', String(futureDate.getFullYear()))
      .replace('MM', String(futureDate.getMonth() + 1).padStart(2, '0'))
      .replace('DD', String(futureDate.getDate()).padStart(2, '0'))
      .replace('HH', String(futureDate.getHours()).padStart(2, '0'))
      .replace('mm', String(futureDate.getMinutes()).padStart(2, '0'));
    composable.startDate.value = formatted;
    await nextTick();
    expect(composable.isInvalid.value).toBe(false);
  });

  it('should not be valid for MONTHLY with past date', async () => {
    composable.selectedFrequency.value = Frequency.MONTHLY;
    await nextTick();
    await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher timeout

    const pastDate = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes in past
    const formatted = composable.dateTimeMask
      .replace('YYYY', String(pastDate.getFullYear()))
      .replace('MM', String(pastDate.getMonth() + 1).padStart(2, '0'))
      .replace('DD', String(pastDate.getDate()).padStart(2, '0'))
      .replace('HH', String(pastDate.getHours()).padStart(2, '0'))
      .replace('mm', String(pastDate.getMinutes()).padStart(2, '0'));
    composable.startDate.value = formatted;
    await nextTick();
    expect(composable.isInvalid.value).toBe(true);
  });

  describe('computed properties', () => {
    it('should show start date input when frequency is selected', () => {
      expect(composable.isStartDateVisible.value).toBe(false);
      composable.selectedFrequency.value = Frequency.DAILY;
      expect(composable.isStartDateVisible.value).toBe(true);
    });

    it('should show days selection only for WEEKLY frequency', () => {
      expect(composable.isDaysVisible.value).toBe(false);

      composable.selectedFrequency.value = Frequency.DAILY;
      expect(composable.isDaysVisible.value).toBe(false);

      composable.selectedFrequency.value = Frequency.ONCE;
      expect(composable.isDaysVisible.value).toBe(false);

      composable.selectedFrequency.value = Frequency.MONTHLY;
      expect(composable.isDaysVisible.value).toBe(false);

      composable.selectedFrequency.value = Frequency.WEEKLY;
      expect(composable.isDaysVisible.value).toBe(true);
    });

    it('should require date and time for ONCE and MONTHLY frequencies', () => {
      expect(composable.requiresDateTime.value).toBe(false);

      composable.selectedFrequency.value = Frequency.DAILY;
      expect(composable.requiresDateTime.value).toBe(false);

      composable.selectedFrequency.value = Frequency.WEEKLY;
      expect(composable.requiresDateTime.value).toBe(false);

      composable.selectedFrequency.value = Frequency.ONCE;
      expect(composable.requiresDateTime.value).toBe(true);

      composable.selectedFrequency.value = Frequency.MONTHLY;
      expect(composable.requiresDateTime.value).toBe(true);
    });
  });

  describe('currentInputValue computed get/set', () => {
    it('should return full datetime for ONCE frequency', async () => {
      composable.selectedFrequency.value = Frequency.ONCE;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150));

      composable.startDate.value = '2025/07/28 14:30';
      expect(composable.currentInputValue.value).toBe('2025/07/28 14:30');
    });

    it('should return full datetime for MONTHLY frequency', async () => {
      composable.selectedFrequency.value = Frequency.MONTHLY;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150));

      composable.startDate.value = '2025/07/28 14:30';
      expect(composable.currentInputValue.value).toBe('2025/07/28 14:30');
    });

    it('should return time only for DAILY frequency', async () => {
      composable.selectedFrequency.value = Frequency.DAILY;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150));

      composable.startDate.value = '2025/07/28 14:30';
      expect(composable.currentInputValue.value).toBe('14:30');
    });

    it('should return time only for WEEKLY frequency', async () => {
      composable.selectedFrequency.value = Frequency.WEEKLY;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150));

      composable.startDate.value = '2025/07/28 14:30';
      expect(composable.currentInputValue.value).toBe('14:30');
    });

    it('should accept valid datetime input for ONCE frequency', async () => {
      composable.selectedFrequency.value = Frequency.ONCE;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150));

      composable.currentInputValue.value = '2025/07/29 16:45';
      expect(composable.startDate.value).toBe('2025/07/29 16:45');
    });

    it('should reject invalid datetime input for ONCE frequency', async () => {
      composable.selectedFrequency.value = Frequency.ONCE;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150));

      const originalValue = composable.startDate.value;
      composable.currentInputValue.value = 'invalid-date';
      expect(composable.startDate.value).toBe(originalValue); // Should not change
    });

    it('should accept valid time input for DAILY frequency', async () => {
      composable.selectedFrequency.value = Frequency.DAILY;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150));

      composable.currentInputValue.value = '16:45';
      // Should combine with today's date
      expect(composable.startDate.value).toMatch(/^\d{4}\/\d{2}\/\d{2} 16:45$/);
    });

    it('should reject invalid time input for DAILY frequency', async () => {
      composable.selectedFrequency.value = Frequency.DAILY;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150));

      const originalValue = composable.startDate.value;
      composable.currentInputValue.value = 'invalid-time';
      expect(composable.startDate.value).toBe(originalValue); // Should not change
    });
  });

  describe('day selection', () => {
    it('should add day when clicked and not selected', () => {
      expect(composable.selectedDays.value).not.toContain(Day.MONDAY);
      composable.onDayClick(Day.MONDAY);
      expect(composable.selectedDays.value).toContain(Day.MONDAY);
    });

    it('should remove day when clicked and already selected', () => {
      composable.selectedDays.value = [Day.MONDAY, Day.TUESDAY];
      expect(composable.selectedDays.value).toContain(Day.MONDAY);

      composable.onDayClick(Day.MONDAY);
      expect(composable.selectedDays.value).not.toContain(Day.MONDAY);
      expect(composable.selectedDays.value).toContain(Day.TUESDAY);
    });

    it('should handle multiple day selections', () => {
      composable.onDayClick(Day.MONDAY);
      composable.onDayClick(Day.WEDNESDAY);
      composable.onDayClick(Day.FRIDAY);

      expect(composable.selectedDays.value).toEqual([Day.MONDAY, Day.WEDNESDAY, Day.FRIDAY]);
    });
  });

  describe('edge cases', () => {
    it('should return undefined config when schedule name is missing', () => {
      composable.scheduleName.value = '';
      composable.selectedFrequency.value = Frequency.DAILY;

      const config = composable.getConfiguration();
      expect(config).toBeUndefined();
    });

    it('should return undefined config when frequency is missing', () => {
      composable.scheduleName.value = 'Test Schedule';
      composable.selectedFrequency.value = undefined;

      const config = composable.getConfiguration();
      expect(config).toBeUndefined();
    });

    it('should handle empty startDate gracefully', () => {
      composable.startDate.value = '';
      expect(composable.isValidStartDate.value).toBeFalsy();
    });

    it('should preserve user time when switching from DAILY to MONTHLY frequency', async () => {
      const userTime = '08:40';

      // Step 1: Set DAILY frequency first
      composable.selectedFrequency.value = Frequency.DAILY;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher

      // Step 2: User enters a specific time for DAILY schedule
      composable.currentInputValue.value = userTime;
      await nextTick();

      // Verify the time is set correctly for DAILY
      const dailyStartDate = composable.startDate.value;
      expect(dailyStartDate).toMatch(/\d{4}\/\d{2}\/\d{2} 08:40/);
      expect(composable.currentInputValue.value).toBe(userTime);

      // Step 3: Switch to MONTHLY frequency
      composable.selectedFrequency.value = Frequency.MONTHLY;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher

      // Step 4: Verify the user's time is preserved
      const monthlyStartDate = composable.startDate.value;
      expect(monthlyStartDate).toMatch(/\d{4}\/\d{2}\/\d{2} 08:40/);
      expect(composable.currentInputValue.value).toMatch(/\d{4}\/\d{2}\/\d{2} 08:40/);

      // Step 5: Generate configuration and verify time consistency
      composable.scheduleName.value = 'Time Consistency Test';
      const config = composable.getConfiguration();

      expect(config).toBeDefined();
      if (config) {
        // Parse the UTC times
        const startDateUtc = config.startDate;
        const cronExpression = config.cronExpression;

        // Convert both back to local time for comparison
        const startDateLocal = dayjs.utc(startDateUtc).local();

        // Parse cron expression to get time
        const cronParts = cronExpression.split(' ');
        const cronHour = parseInt(cronParts[2], 10);
        const cronMinute = parseInt(cronParts[1], 10);
        const cronLocal = dayjs.utc('2025-01-01').hour(cronHour).minute(cronMinute).local();

        // Verify both times match when converted to local
        expect(startDateLocal.format('HH:mm')).toBe(cronLocal.format('HH:mm'));
        expect(startDateLocal.format('HH:mm')).toBe(userTime);

        // Verify the cron expression is for monthly execution on the correct day
        expect(cronExpression).toMatch(/^0 \d+ \d+ \d+ \* \*$/);
      }
    });
  });

  describe('validation logic for different frequencies', () => {
    describe('ONCE frequency validation', () => {
      it('should be valid with future date', async () => {
        composable.selectedFrequency.value = Frequency.ONCE;
        await nextTick();
        await new Promise((resolve) => setTimeout(resolve, 150));

        const futureDate = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes in future
        const formatted = composable.dateTimeMask
          .replace('YYYY', String(futureDate.getFullYear()))
          .replace('MM', String(futureDate.getMonth() + 1).padStart(2, '0'))
          .replace('DD', String(futureDate.getDate()).padStart(2, '0'))
          .replace('HH', String(futureDate.getHours()).padStart(2, '0'))
          .replace('mm', String(futureDate.getMinutes()).padStart(2, '0'));
        composable.startDate.value = formatted;
        await nextTick();

        expect(composable.isValidStartDate.value).toBe(true);
        expect(composable.isInvalid.value).toBe(false);
      });

      it('should be invalid with past date', async () => {
        composable.selectedFrequency.value = Frequency.ONCE;
        await nextTick();
        await new Promise((resolve) => setTimeout(resolve, 150));

        const pastDate = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes in past
        const formatted = composable.dateTimeMask
          .replace('YYYY', String(pastDate.getFullYear()))
          .replace('MM', String(pastDate.getMonth() + 1).padStart(2, '0'))
          .replace('DD', String(pastDate.getDate()).padStart(2, '0'))
          .replace('HH', String(pastDate.getHours()).padStart(2, '0'))
          .replace('mm', String(pastDate.getMinutes()).padStart(2, '0'));
        composable.startDate.value = formatted;
        await nextTick();

        expect(composable.isValidStartDate.value).toBe(false);
        expect(composable.isInvalid.value).toBe(true);
      });
    });

    describe('DAILY frequency validation', () => {
      it('should be valid with future date', async () => {
        composable.selectedFrequency.value = Frequency.DAILY;
        await nextTick();
        await new Promise((resolve) => setTimeout(resolve, 150));

        const futureDate = new Date(Date.now() + 10 * 60 * 1000);
        const formatted = composable.dateTimeMask
          .replace('YYYY', String(futureDate.getFullYear()))
          .replace('MM', String(futureDate.getMonth() + 1).padStart(2, '0'))
          .replace('DD', String(futureDate.getDate()).padStart(2, '0'))
          .replace('HH', String(futureDate.getHours()).padStart(2, '0'))
          .replace('mm', String(futureDate.getMinutes()).padStart(2, '0'));
        composable.startDate.value = formatted;
        await nextTick();

        expect(composable.isValidStartDate.value).toBe(true);
        expect(composable.isInvalid.value).toBe(false);
      });

      it('should be valid with past date', async () => {
        composable.selectedFrequency.value = Frequency.DAILY;
        await nextTick();
        await new Promise((resolve) => setTimeout(resolve, 150));

        const pastDate = new Date(Date.now() - 10 * 60 * 1000);
        const formatted = composable.dateTimeMask
          .replace('YYYY', String(pastDate.getFullYear()))
          .replace('MM', String(pastDate.getMonth() + 1).padStart(2, '0'))
          .replace('DD', String(pastDate.getDate()).padStart(2, '0'))
          .replace('HH', String(pastDate.getHours()).padStart(2, '0'))
          .replace('mm', String(pastDate.getMinutes()).padStart(2, '0'));
        composable.startDate.value = formatted;
        await nextTick();

        expect(composable.isValidStartDate.value).toBe(true);
        expect(composable.isInvalid.value).toBe(false);
      });

      it('should be invalid with empty startDate', async () => {
        composable.selectedFrequency.value = Frequency.DAILY;
        await nextTick();
        await new Promise((resolve) => setTimeout(resolve, 150));

        composable.startDate.value = '';
        await nextTick();

        expect(composable.isValidStartDate.value).toBe(false);
        expect(composable.isInvalid.value).toBe(true);
      });
    });

    describe('WEEKLY frequency validation', () => {
      it('should be valid with past date and selected days', async () => {
        composable.selectedFrequency.value = Frequency.WEEKLY;
        composable.selectedDays.value = [Day.MONDAY, Day.WEDNESDAY];
        await nextTick();
        await new Promise((resolve) => setTimeout(resolve, 150));

        const pastDate = new Date(Date.now() - 10 * 60 * 1000);
        const formatted = composable.dateTimeMask
          .replace('YYYY', String(pastDate.getFullYear()))
          .replace('MM', String(pastDate.getMonth() + 1).padStart(2, '0'))
          .replace('DD', String(pastDate.getDate()).padStart(2, '0'))
          .replace('HH', String(pastDate.getHours()).padStart(2, '0'))
          .replace('mm', String(pastDate.getMinutes()).padStart(2, '0'));
        composable.startDate.value = formatted;
        await nextTick();

        expect(composable.isValidStartDate.value).toBe(true);
        expect(composable.isInvalid.value).toBe(false);
      });

      it('should be invalid with valid date but no selected days', async () => {
        composable.selectedFrequency.value = Frequency.WEEKLY;
        composable.selectedDays.value = [];
        await nextTick();
        await new Promise((resolve) => setTimeout(resolve, 150));

        const futureDate = new Date(Date.now() + 10 * 60 * 1000);
        const formatted = composable.dateTimeMask
          .replace('YYYY', String(futureDate.getFullYear()))
          .replace('MM', String(futureDate.getMonth() + 1).padStart(2, '0'))
          .replace('DD', String(futureDate.getDate()).padStart(2, '0'))
          .replace('HH', String(futureDate.getHours()).padStart(2, '0'))
          .replace('mm', String(futureDate.getMinutes()).padStart(2, '0'));
        composable.startDate.value = formatted;
        await nextTick();

        expect(composable.isValidStartDate.value).toBe(true);
        expect(composable.isInvalid.value).toBe(true); // Invalid due to no selected days
      });
    });

    describe('MONTHLY frequency validation', () => {
      it('should be valid with future date', async () => {
        composable.selectedFrequency.value = Frequency.MONTHLY;
        await nextTick();
        await new Promise((resolve) => setTimeout(resolve, 150));

        const futureDate = new Date(Date.now() + 10 * 60 * 1000);
        const formatted = composable.dateTimeMask
          .replace('YYYY', String(futureDate.getFullYear()))
          .replace('MM', String(futureDate.getMonth() + 1).padStart(2, '0'))
          .replace('DD', String(futureDate.getDate()).padStart(2, '0'))
          .replace('HH', String(futureDate.getHours()).padStart(2, '0'))
          .replace('mm', String(futureDate.getMinutes()).padStart(2, '0'));
        composable.startDate.value = formatted;
        await nextTick();

        expect(composable.isValidStartDate.value).toBe(true);
        expect(composable.isInvalid.value).toBe(false);
      });

      it('should be not valid with past date', async () => {
        composable.selectedFrequency.value = Frequency.MONTHLY;
        await nextTick();
        await new Promise((resolve) => setTimeout(resolve, 150));

        const pastDate = new Date(Date.now() - 10 * 60 * 1000);
        const formatted = composable.dateTimeMask
          .replace('YYYY', String(pastDate.getFullYear()))
          .replace('MM', String(pastDate.getMonth() + 1).padStart(2, '0'))
          .replace('DD', String(pastDate.getDate()).padStart(2, '0'))
          .replace('HH', String(pastDate.getHours()).padStart(2, '0'))
          .replace('mm', String(pastDate.getMinutes()).padStart(2, '0'));
        composable.startDate.value = formatted;
        await nextTick();

        expect(composable.isValidStartDate.value).toBe(false);
        expect(composable.isInvalid.value).toBe(true);
      });

      it('should be invalid with empty startDate', async () => {
        composable.selectedFrequency.value = Frequency.MONTHLY;
        await nextTick();
        await new Promise((resolve) => setTimeout(resolve, 150));

        composable.startDate.value = '';
        await nextTick();

        expect(composable.isValidStartDate.value).toBe(false);
        expect(composable.isInvalid.value).toBe(true);
      });
    });

    describe('comparison between frequencies', () => {
      it('should validate ONCE differently from recurring schedules', async () => {
        const pastDate = new Date(Date.now() - 10 * 60 * 1000);
        const formatted = composable.dateTimeMask
          .replace('YYYY', String(pastDate.getFullYear()))
          .replace('MM', String(pastDate.getMonth() + 1).padStart(2, '0'))
          .replace('DD', String(pastDate.getDate()).padStart(2, '0'))
          .replace('HH', String(pastDate.getHours()).padStart(2, '0'))
          .replace('mm', String(pastDate.getMinutes()).padStart(2, '0'));

        // Test ONCE with past date - should be invalid
        composable.selectedFrequency.value = Frequency.ONCE;
        await nextTick();
        await new Promise((resolve) => setTimeout(resolve, 150));
        composable.startDate.value = formatted;
        await nextTick();

        expect(composable.isValidStartDate.value).toBe(false);
        expect(composable.isInvalid.value).toBe(true);

        // Test DAILY with past date - should be valid
        composable.selectedFrequency.value = Frequency.DAILY;
        await nextTick();
        await new Promise((resolve) => setTimeout(resolve, 150));

        expect(composable.isValidStartDate.value).toBe(true);
        expect(composable.isInvalid.value).toBe(false);

        // Test MONTHLY with past date - should be valid
        composable.selectedFrequency.value = Frequency.MONTHLY;
        await nextTick();
        await new Promise((resolve) => setTimeout(resolve, 150));

        expect(composable.isValidStartDate.value).toBe(true);
        expect(composable.isInvalid.value).toBe(false);
      });
    });
  });

  describe('input validation and user interaction', () => {
    it('should disable save button when user clears the time input for DAILY frequency', async () => {
      // 1. User selects DAILY frequency
      composable.selectedFrequency.value = Frequency.DAILY;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher

      // At this point, watcher should have set a valid time and save button should be enabled
      expect(composable.isValidStartDate.value).toBe(true);
      expect(composable.isInvalid.value).toBe(false);

      // 2. User deletes/clears the time in the UI (simulates clearing the input field)
      composable.currentInputValue.value = '';
      await nextTick();

      // 3. Save button should now be disabled because user cleared the input
      expect(composable.isValidStartDate.value).toBe(false);
      expect(composable.isInvalid.value).toBe(true);
    });

    it('should disable save button when user enters invalid time for DAILY frequency', async () => {
      // 1. User selects DAILY frequency
      composable.selectedFrequency.value = Frequency.DAILY;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher

      // At this point, save button should be enabled
      expect(composable.isValidStartDate.value).toBe(true);
      expect(composable.isInvalid.value).toBe(false);

      // 2. User enters invalid time format
      composable.currentInputValue.value = '25:99'; // Invalid time
      await nextTick();

      // 3. Save button should be disabled due to invalid input
      expect(composable.isValidStartDate.value).toBe(false);
      expect(composable.isInvalid.value).toBe(true);
    });

    it('should enable save button when user enters valid time after clearing for DAILY frequency', async () => {
      // 1. User selects DAILY frequency
      composable.selectedFrequency.value = Frequency.DAILY;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher

      // 2. User clears the input
      composable.currentInputValue.value = '';
      await nextTick();
      expect(composable.isInvalid.value).toBe(true);

      // 3. User enters a valid time
      composable.currentInputValue.value = '14:30';
      await nextTick();

      // 4. Save button should be enabled again
      expect(composable.isValidStartDate.value).toBe(true);
      expect(composable.isInvalid.value).toBe(false);
    });

    it('should disable save button when user clears the datetime input for ONCE frequency', async () => {
      // 1. User selects ONCE frequency
      composable.selectedFrequency.value = Frequency.ONCE;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher

      // At this point, watcher should have set a valid datetime and save button should be enabled
      expect(composable.isValidStartDate.value).toBe(true);
      expect(composable.isInvalid.value).toBe(false);

      // 2. User deletes/clears the datetime in the UI
      composable.currentInputValue.value = '';
      await nextTick();

      // 3. Save button should now be disabled because user cleared the input
      expect(composable.isValidStartDate.value).toBe(false);
      expect(composable.isInvalid.value).toBe(true);
    });

    it('should disable save button when user enters invalid datetime for ONCE frequency', async () => {
      // 1. User selects ONCE frequency
      composable.selectedFrequency.value = Frequency.ONCE;
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve, 150)); // Wait for watcher

      // At this point, save button should be enabled
      expect(composable.isValidStartDate.value).toBe(true);
      expect(composable.isInvalid.value).toBe(false);

      // 2. User enters invalid datetime format
      composable.currentInputValue.value = 'invalid-datetime';
      await nextTick();

      // 3. Save button should be disabled due to invalid input
      expect(composable.isValidStartDate.value).toBe(false);
      expect(composable.isInvalid.value).toBe(true);
    });
  });
});

export const dsaMappingColumns = [
  {
    name: 'dropzone',
    field: 'dropzone',
    label: '',
    align: 'left' as const,
  },
  {
    name: 'inRiverFieldType',
    field: 'inRiverFieldType',
    label: 'target',
    align: 'left' as const,
  },
  {
    name: 'inRiverDataType',
    field: 'inRiverDataType',
    label: '',
    align: 'left' as const,
  },
  {
    name: 'actions',
    field: 'actions',
    label: '',
    align: 'left' as const,
  },
  {
    name: 'FormatField',
    field: 'FormatField',
    label: 'dsa',
    align: 'left' as const,
  },
  {
    name: 'FormatDataType',
    field: 'FormatDataType',
    label: '',
    align: 'left' as const,
  },
  {
    name: 'function',
    field: 'function',
    label: 'function',
    align: 'left' as const,
  },
  {
    name: 'Description',
    field: 'Description',
    label: '',
    align: 'left' as const,
  },
  {
    name: 'Mandatory',
    field: 'Mandatory',
    label: '',
    align: 'left' as const,
  },
  {
    name: 'Recommended',
    field: 'Recommended',
    label: '',
    align: 'left' as const,
  },
  {
    name: 'Unique',
    field: 'Unique',
    label: '',
    align: 'left' as const,
  },
];

export interface DsaFormatResponse {
  formats: DsaFormatFieldsResponse[];
}

export interface DsaFormatFieldsResponse {
  id: string;
  format: string;
  field: string;
  unitType: string;
  unitCvl: string;
  unitDefaultValue: string;
  datatype: string;
  mandatory: boolean;
  unique: boolean;
  maxLength: number;
  minLength: number;
  defaultValue: string;
  recommended: boolean;
  groupRequiredFields: string[];
  enumerationValues: string[];
  path: string;
  description: string;
  maxValue: string;
  minValue: string;
  maxInstances: number;
  decimalFormat: string;
  regEx: string;
  conditionalRule: string;
  category: string;
  childAttributes: string[];
  objectSchema: string;
  displayName: string;
}

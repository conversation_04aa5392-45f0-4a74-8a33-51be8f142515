export interface DsaMappingResponse {
  id: number;
  data: string; // JSON string representing the DsaMapping
  environmentGid: string;
  createdBy: string;
  createdDate: string;
  updatedBy: string;
  updatedDate: string;
}

export interface MappingTargetFormatField {
  // values from the not dsa mapping format file
  FormatField: string; // format.field
  FormatFieldId: string; // format.id
  FormatDataType: string; // format.dataType
}

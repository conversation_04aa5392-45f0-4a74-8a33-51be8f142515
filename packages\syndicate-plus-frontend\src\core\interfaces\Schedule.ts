export interface ScheduleResponse {
  Id: number;
  Name: string;
  CollectionName: string;
  MappingName: string;
  OutputName: string;
  CronExpression: string;
  StartDate: string;
  LastExecution: string;
  UpdatedBy: string;
}

export interface Schedule {
  id: number;
  name: string;
  collectionName: string;
  mappingName: string;
  outputName: string;
  cronExpression?: string;
  startDate?: string;
  lastExecution?: string;
  updatedBy: string;
}

<template>
  <c-layout-with-back-button :title="title">
    <template #left-sidebar>
      <div id="left-sidebar"></div>
    </template>
    <template #right-sidebar>
      <div id="right-sidebar">
        <!-- Right sidebar content can be added here if needed -->
      </div>
    </template>
    <div class="data-submissions-layout">
      <!-- Left panel with submissions list -->
      <div class="submissions-sidebar">
        <div v-if="isLoadingSubmissions && dataSubmissions.length === 0" class="sidebar-loading">
          <q-inner-loading showing color="primary">
            <c-spinner color="primary" size="30" />
          </q-inner-loading>
        </div>
        <div v-else-if="dataSubmissions.length > 0" class="submissions-list">
          <div ref="submissionsScrollContainer" class="submissions-items">
            <div
              v-for="submission in dataSubmissions"
              :key="submission.id"
              class="submission-item"
              :class="{ active: selectedSubmission?.id === submission.id }"
              @click="handleSubmissionSelected(submission)"
            >
              <div class="submission-header">
                <div class="submission-id">ID: {{ submission.id }}</div>
                <div class="creation-date">{{ formatSubmissionDate(submission.createdDate) }}</div>
              </div>
              <q-icon name="mdi-information-outline" size="24px" color="grey-6" class="info-icon">
                <q-tooltip anchor="bottom middle" self="top left" :offset="[0, 10]">
                  <div class="tooltip-content">
                    <div><strong>State:</strong> {{ submission.state }}</div>
                    <div><strong>Date Created:</strong> {{ formatToMonthDayYear(submission.createdDate) }}</div>
                    <div v-if="submission.updatedDate">
                      <strong>Date Updated:</strong> {{ formatToMonthDayYear(submission.updatedDate) }}
                    </div>
                  </div>
                </q-tooltip>
              </q-icon>
            </div>

            <!-- Load more indicator -->
            <div v-if="!dataSubmissionStore.isLastPage" ref="infiniteScrollTrigger" class="infinite-scroll-trigger">
              <div class="loading-indicator">
                <q-spinner-dots v-if="isLoadingSubmissions" color="primary" size="24px" />
              </div>
            </div>
          </div>
        </div>
        <div v-else class="sidebar-empty">
          <div class="empty-content">
            <q-icon name="mdi-inbox-outline" size="32px" color="grey-5" />
            <p>{{ $t('core.api_failure_response_dialog.no_submissions_found') }}</p>
          </div>
        </div>
      </div>

      <!-- Main content area -->
      <div class="main-content">
        <div v-if="selectedSubmission" class="entities-content">
          <data-submission-entities-table
            :entities="filteredEntities"
            :is-loading="isLoadingEntities"
            :data-submission-id="selectedSubmission.id"
            @update:selected="handleEntitySelected"
            @load-more="loadMoreEntities"
          />
        </div>
        <div v-else-if="dataSubmissions.length > 0" class="no-selection">
          <div class="no-selection-content">
            <q-icon name="mdi-table-search" size="64px" color="grey-4" />
            <h3>Select a Data Submission</h3>
            <p>Choose a submission from the sidebar to view its entities</p>
          </div>
        </div>
        <div v-else-if="!isLoadingSubmissions" class="empty-state">
          <c-no-data src="nothing-to-see" image-height="195px" text="No data submissions found for the selected job" />
        </div>
        <div v-if="isLoadingSubmissions && dataSubmissions.length === 0" class="main-loading">
          <q-inner-loading showing color="primary">
            <c-spinner color="primary" size="40" />
          </q-inner-loading>
        </div>
      </div>

      <c-api-failure-response-panel
        v-if="showApiFailureDialog"
        v-model:show="showApiFailureDialog"
        :entity="selectedEntity"
      />
    </div>
  </c-layout-with-back-button>
</template>

<script lang="ts" setup>
import { onBeforeMount, onMounted, onUnmounted, ref, computed, watch } from 'vue';
import { useRouter } from '@composables/useRouter';
import { useAppInsightsStore } from '@stores';
import { PageName } from '@enums';
import { DataSubmission, DataSubmissionEntity } from '@core/interfaces';
import { useDataSubmissionStore } from '@core/stores';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import { CNoData } from '@components';
import { formatToMonthDayYear } from '@services/helper/dateHelpers';
import { CApiFailureResponsePanel, DataSubmissionEntitiesTable } from '@core/components/DataSubmissions';

// Composables
const dataSubmissionStore = useDataSubmissionStore();
const { setScreenName } = useAppInsightsStore();
const { route } = useRouter();
const { t } = useI18n();

// Variables
const jobIdParam = route.params?.jobId;
const jobId = typeof jobIdParam === 'string' ? Number(jobIdParam) : undefined;
const tradingPartnerId = route.params?.tradingPartnerId as string;

// Refs
const selectedSubmission = ref<DataSubmission | null>(null);
const selectedEntity = ref<DataSubmissionEntity | null>(null);
const showApiFailureDialog = ref(false);
const submissionsScrollContainer = ref<HTMLElement | null>(null);
const infiniteScrollTrigger = ref<HTMLElement | null>(null);
const intersectionObserver = ref<IntersectionObserver | null>(null);
const { dataSubmissions, isLoadingSubmissions, isLoadingEntities, dataSubmissionEntities } =
  storeToRefs(dataSubmissionStore);

// Computed
const decodedTradingPartnerId = computed(() => decodeURIComponent(tradingPartnerId));
const title = computed(() => {
  return jobId
    ? `data submissions - ${decodedTradingPartnerId.value} - job id: ${jobId}`
    : `data submissions - ${decodedTradingPartnerId.value}`;
});

// Computed properties for filtering and formatting
const filteredEntities = computed(() => {
  return dataSubmissionEntities.value;
});

// Helper functions
const formatSubmissionDate = (dateString: string) => {
  return `${t('core.api_failure_response_dialog.creation_date')}: ${formatToMonthDayYear(dateString)}`;
};

// Functions
const handleSubmissionSelected = async (submission: DataSubmission | null): Promise<void> => {
  selectedSubmission.value = submission;
  if (submission) {
    const isDataSubmissionIdChanged = true;
    await dataSubmissionStore.loadDataSubmissionEntities(submission.id, isDataSubmissionIdChanged);
  }
};

const handleEntitySelected = (entity: DataSubmissionEntity | null) => {
  selectedEntity.value = entity;

  // Close the panel if no entity is selected
  if (!entity) {
    showApiFailureDialog.value = false;
    return;
  }

  // Show dialog if entity has API failure response
  showApiFailureDialog.value = !!entity.apiFailureResponse;
};

// Setup Intersection Observer for better infinite scroll
const setupIntersectionObserver = () => {
  // Cleanup previous observer if it exists
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect();
  }

  // Create new observer
  intersectionObserver.value = new IntersectionObserver(
    async (entries) => {
      const [entry] = entries;
      if (entry.isIntersecting && !isLoadingSubmissions.value && !dataSubmissionStore.isLastPage) {
        await dataSubmissionStore.loadDataSubmissions(jobId);
      }
    },
    {
      root: submissionsScrollContainer.value,
      rootMargin: '0px 0px 100px 0px', // Trigger 100px before the element comes into view
      threshold: 0.1, // Trigger when 10% of the element is visible
    }
  );

  // Start observing the trigger element when available
  if (infiniteScrollTrigger.value) {
    intersectionObserver.value.observe(infiniteScrollTrigger.value);
  }
};

const loadMoreEntities = async () => {
  if (!isLoadingEntities.value && !dataSubmissionStore.isLastEntitiesPage && selectedSubmission.value) {
    await dataSubmissionStore.loadDataSubmissionEntities(selectedSubmission.value.id);
  }
};

const refreshData = async () => {
  dataSubmissionStore.resetState();
  selectedSubmission.value = null;
  selectedEntity.value = null;
  await dataSubmissionStore.loadDataSubmissions(jobId);
};

// Watch dataSubmissions to automatically select the first one when loaded
watch(
  dataSubmissions,
  (submissions) => {
    if (submissions.length > 0 && !selectedSubmission.value) {
      // Only select the first submission if none is currently selected
      handleSubmissionSelected(submissions[0]);
    }
  },
  { immediate: true }
);

// Lifecycle methods
watch(
  () => route.params.jobId,
  async (newJobId) => {
    if (newJobId) {
      await refreshData();
    }
  }
);

// Watch for changes in the trigger element or scroll container to set up the observer
watch([infiniteScrollTrigger, submissionsScrollContainer], () => {
  if (infiniteScrollTrigger.value && submissionsScrollContainer.value) {
    setupIntersectionObserver();
  }
});

// Update observer when data changes to ensure it's targeting the newest trigger
watch(dataSubmissions, () => {
  if (infiniteScrollTrigger.value && intersectionObserver.value) {
    // Small timeout to ensure DOM has updated
    setTimeout(() => {
      if (infiniteScrollTrigger.value) {
        intersectionObserver.value?.observe(infiniteScrollTrigger.value);
      }
    }, 100);
  }
});

onBeforeMount(async () => {
  setScreenName(PageName.DATA_SUBMISSIONS_PAGE);
  await refreshData();
});

onMounted(() => {
  // Setup intersection observer after component is mounted
  if (infiniteScrollTrigger.value && submissionsScrollContainer.value) {
    setupIntersectionObserver();
  }
});

onUnmounted(() => {
  // Clean up the intersection observer when component is unmounted
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect();
    intersectionObserver.value = null;
  }
  dataSubmissionStore.resetState();
});
</script>

<style lang="scss" scoped>
.data-submissions-layout {
  display: flex;
  height: 100%;
  background-color: var(--color-background, #f7f9fc);
}

.submissions-sidebar {
  width: 280px;
  min-width: 280px;
  padding: 0;
  border-right: 1px solid var(--color-grey-lighter, #e0e0e0);
  background-color: var(--color-white, #ffffff);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.submissions-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.submissions-items {
  flex: 1;
  overflow-y: auto;
  max-height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
  scroll-behavior: smooth;
}

.submission-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  margin: 0;
  background-color: var(--color-white, #ffffff);
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  border-bottom: 1px solid var(--color-grey-lighter, #e0e0e0);

  &:hover {
    background-color: var(--color-grey-lightest, #f5f5f5);
  }

  &.active {
    background-color: var(--color-green-10, #e3f2fd);
  }
}

.submission-header {
  display: flex;
  flex-direction: column;
}

.submission-id {
  font-weight: 600;
  font-size: 14px;
  color: var(--color-text-primary, #212121);
}

.info-icon {
  opacity: 0.7;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }
}

.creation-date {
  font-size: 12px;
  color: var(--color-text-secondary, #666);
}

.sidebar-loading {
  position: relative;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-white, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.infinite-scroll-trigger {
  padding: 12px;
  text-align: center;
  border-top: 1px solid var(--color-grey-lightest, #f0f0f0);
  margin-top: 4px;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.sidebar-empty {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-white, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  padding: 24px;
}

.empty-content {
  text-align: center;
  color: var(--color-text-secondary, #666);

  p {
    margin: 12px 0 0 0;
    font-size: 14px;
    font-weight: 500;
  }
}

.main-content {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.entities-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--color-white, #ffffff);
  padding: 20px;
}

.entities-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
  width: 100%;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-grey-lighter, #e0e0e0);
}

.entities-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.no-selection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.no-selection-content {
  text-align: center;
  max-width: 300px;

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--color-text-primary, #212121);
    margin: 16px 0 8px 0;
  }

  p {
    font-size: 14px;
    color: var(--color-text-secondary, #666);
    margin: 0;
    line-height: 1.5;
  }
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.main-loading {
  position: relative;
  height: 200px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tooltip-content {
  padding: 4px 0;

  div {
    margin-bottom: 4px;
    font-size: 13px;
    white-space: nowrap;

    &:last-child {
      margin-bottom: 0;
    }

    strong {
      font-weight: 600;
      margin-right: 4px;
    }
  }
}
</style>

<!-- Use a separate non-scoped style block for global styles -->
<style lang="scss">
/* Global styles that need to target elements outside the component */
.c-dialog {
  &.api-failure-dialog {
    .q-dialog__backdrop {
      background: unset !important;
    }
  }
}
</style>

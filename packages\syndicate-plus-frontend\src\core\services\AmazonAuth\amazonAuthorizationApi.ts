import { getEnvironmentGlobalId } from '@helpers/EnvironmentHelper';

export const startAmazonAuthFlow = async (tradingPartnerId: string, redirectUrl: string): Promise<void> => {
  const environmentGid = getEnvironmentGlobalId();
  const prefix = import.meta.env.DEV ? '/proxy' : '';
  const url =
    prefix +
    `/outputadapter/proxy/api/environments/${environmentGid}/trading-partners/${tradingPartnerId}/authorizationflow/start?redirectUrl=${encodeURIComponent(
      redirectUrl
    )}`;
  //await outputAdapterClient.get(url, 'Error starting Amazon authorization flow')
  window.open(url, '_blank');
};

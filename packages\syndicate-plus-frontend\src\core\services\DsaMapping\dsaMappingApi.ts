import { outputAdapterClient } from '@core/Utils';
import { getEnvironmentGlobalId } from '@helpers/EnvironmentHelper';
import { getUserName } from '@helpers';
import { DsaMappingResponse } from '@core/interfaces/DsaMapping';
import { DynamicFormat } from '@core/interfaces/Category';
import { DynamicMappingDetailsResponse, DynamicMappingFieldResponse } from '@core/interfaces';
import { toDsaMapping, toMappingFields } from './utils';

export const getDefaultDsaMappingFields = async (): Promise<DynamicMappingFieldResponse[]> => {
  const url = '/api/dsa/format';
  const response = await outputAdapterClient.get<DynamicFormat>(url, 'Error fetching dsa mapping');
  return response.data ? toMappingFields(response.data) : [];
};

export const fetchDsaMappingById = async (dsaMappingId: number): Promise<DynamicMappingDetailsResponse | null> => {
  const environmentGid = getEnvironmentGlobalId();
  const url = `/api/environments/${environmentGid}/dsa-mappings/${dsaMappingId}`;
  const response = await outputAdapterClient.get<DsaMappingResponse>(url, 'Error fetching dsa mapping');
  return response.data ? toDsaMapping(response.data) : null;
};

export const saveDsaMapping = async (dsaMapping: DynamicMappingDetailsResponse): Promise<DsaMappingResponse> => {
  const environmentGid = getEnvironmentGlobalId();
  const userName = getUserName();
  const url = `/api/environments/${environmentGid}/dsa-mappings`;
  const dsaMappingToSave = {
    data: JSON.stringify(dsaMapping),
    createdBy: userName,
  };
  const response = await outputAdapterClient.post<DsaMappingResponse>(
    url,
    dsaMappingToSave,
    'Error saving dsa mapping'
  );
  return response.data ?? ({} as DsaMappingResponse);
};

export const updateDsaMapping = async (dsaMapping: DynamicMappingDetailsResponse): Promise<boolean> => {
  const environmentGid = getEnvironmentGlobalId();
  const id = dsaMapping.MappingId;
  const url = `/api/environments/${environmentGid}/dsa-mappings/${id}`;
  const userName = getUserName();
  const payload = {
    data: JSON.stringify(dsaMapping),
    updatedBy: userName ?? '',
  };
  const response = await outputAdapterClient.put(url, payload, 'Error updating dsa mapping');
  return response.ok;
};

export const deleteDsaMapping = async (dsaMappingId: number): Promise<boolean> => {
  const environmentGid = getEnvironmentGlobalId();
  const url = `/api/environments/${environmentGid}/dsa-mappings/${dsaMappingId}`;
  const response = await outputAdapterClient.delete(url, 'Error deleting dsa mapping');
  return response.ok;
};

import { describe, expect, it, beforeEach, afterEach, vi } from 'vitest';
import {
  toMappingFields,
  createMappingRow,
  initDefaultDsaMapping,
  initCurrentDsaMapping,
  toDsaMapping,
} from './dsaMappingUtils';
import { DynamicFormat, DynamicFormatRow } from '@core/interfaces/Category';
import { DynamicMappingFieldResponse, MappingDetailsResponse, DynamicMappingDetailsResponse } from '@core/interfaces';
import { DsaMappingResponse } from '@core/interfaces/DsaMapping';

describe('dsaMappingUtils', () => {
  // Mock console methods to suppress expected warnings/errors during tests
  let originalConsoleWarn: typeof console.warn;
  let originalConsoleError: typeof console.error;

  beforeEach(() => {
    // Save original console methods
    originalConsoleWarn = console.warn;
    originalConsoleError = console.error;

    // Replace with mock functions
    console.warn = vi.fn();
    console.error = vi.fn();
  });

  afterEach(() => {
    // Restore original console methods
    console.warn = originalConsoleWarn;
    console.error = originalConsoleError;
  });

  describe('toMappingFields', () => {
    it('should return empty array if format is invalid', () => {
      const result = toMappingFields(null as unknown as DynamicFormat);
      expect(result).toEqual([]);
    });

    it('should return empty array if formats array is missing', () => {
      const format = { formats: null } as unknown as DynamicFormat;
      const result = toMappingFields(format);
      expect(result).toEqual([]);
    });

    it('should map format rows to mapping fields', () => {
      const format = {
        formats: [
          {
            id: '1',
            datatype: 'string',
            field: 'name',
            category: 'basic',
            mandatory: true,
            format: '',
            unitType: '',
            unitCvl: '',
            unitDefaultValue: '',
            unique: false,
            maxLength: null,
            minLength: null,
            defaultValue: '',
            recommended: false,
            groupRequiredFields: '',
            enumerationValues: '',
            path: '',
            description: '',
            maxValue: '',
            minValue: '',
            maxInstances: null,
            minInstances: null,
            decimalFormat: '',
            regEx: '',
            conditionalRule: '',
            childAttributes: [],
          },
        ],
      };

      const result = toMappingFields(format as any);

      expect(result).toHaveLength(1);
      expect(result[0].FormatFieldId).toBe('1');
      expect(result[0].FormatDataType).toBe('string');
      expect(result[0].Mandatory).toBe(true);
    });
  });

  describe('createMappingRow', () => {
    it('should create a mapping row from format row', () => {
      const formatRow: DynamicFormatRow = {
        id: '1',
        datatype: 'string',
        field: 'name',
        category: 'basic',
        path: '/path/to/field',
        unitType: 'cm',
        unitCvl: 'length',
        unitDefaultValue: 'cm',
        unique: true,
        recommended: false,
        mandatory: true,
        defaultValue: 'default',
        maxLength: 100,
        description: 'Description',
        minLength: 1,
        maxValue: '1000',
        minValue: '0',
        maxInstances: 5,
        minInstances: 1,
        decimalFormat: '#.##',
        regEx: '/\\w+/',
        conditionalRule: 'rule',
        childAttributes: ['attr1', 'attr2'],
        format: 'format',
        groupRequiredFields: 'field1,field2',
        enumerationValues: '',
        objectSchema: null,
      };

      const result = createMappingRow(formatRow);

      // Test core mappings
      expect(result.FormatFieldId).toBe('1');
      expect(result.FormatDataType).toBe('string');
      expect(result.FormatField).toBe('name');
      expect(result.Category).toBe('basic');
      expect(result.Path).toBe('/path/to/field');
      expect(result.UnitType).toBe('cm');
      expect(result.UnitCvl).toBe('length');
      expect(result.UnitDefaultValue).toBe('cm');
      expect(result.Unique).toBe(true);
      expect(result.Recommended).toBe(false);
      expect(result.Mandatory).toBe(true);
      expect(result.DefaultValue).toBe('default');
      expect(result.MaxLength).toBe(100);
      expect(result.Description).toBe('Description');

      // Test advanced fields
      expect(result.MinLength).toBe(1);
      expect(result.MaxValue).toBe('1000');
      expect(result.MinValue).toBe('0');
      expect(result.MaxInstances).toBe(5);
      expect(result.MinInstances).toBe(1);
      expect(result.DecimalFormat).toBe('#.##');
      expect(result.RegEx).toBe('/\\w+/');
      expect(result.ConditionalRule).toBe('rule');
      expect(result.ChildAttributes).toEqual(['attr1', 'attr2']);
      expect(result.Format).toBe('format');
      expect(result.GroupRequiredFields).toBe('field1,field2');
    });

    it('should handle missing optional fields with default values', () => {
      const formatRow = {
        id: '1',
        datatype: 'string',
        field: 'name',
        format: '',
        unitType: '',
        unitCvl: '',
        unitDefaultValue: '',
        mandatory: false,
        unique: false,
        maxLength: 0,
        minLength: 0,
        defaultValue: '',
        recommended: false,
        groupRequiredFields: '',
        enumerationValues: '',
        path: '',
        description: '',
        maxValue: '',
        minValue: '',
        maxInstances: 0,
        minInstances: 0,
        decimalFormat: '',
        regEx: '',
        conditionalRule: '',
        childAttributes: [],
        objectSchema: null,
      };

      const result = createMappingRow(formatRow as any);

      expect(result.FormatFieldId).toBe('1');
      expect(result.FormatDataType).toBe('string');
      expect(result.FormatField).toBe('name');
      expect(result.Category).toBe('');
      expect(result.Path).toBe(null);
      expect(result.UnitType).toBe(null);
      expect(result.UnitCvl).toBe(null);
      expect(result.UnitDefaultValue).toBe(null);
      expect(result.Unique).toBe(false);
      expect(result.Recommended).toBe(false);
      expect(result.Mandatory).toBe(false);
      expect(result.DefaultValue).toBe(null);
      expect(result.MaxLength).toBe(null);
      expect(result.Description).toBe(null);
    });
  });

  describe('initDefaultDsaMapping', () => {
    it('should initialize default DSA mapping with given mapping and fields', () => {
      // Create a mock MappingDetailsResponse with all required properties
      const mapping = {
        MappingId: 123,
        MappingName: 'Test Mapping',
        FirstRelatedEntityTypeId: 'entity1',
        FirstLinkEntityTypeId: 'link1',
        SecondRelatedEntityTypeId: 'entity2',
        SecondLinkEntityTypeId: 'link2',
        WorkareaEntityTypeId: 'workarea',
        OutputEntityTypeId: 'output',
        DefaultLanguage: 'sv',
        FormatId: 456,
        ImageUrl: null,
        MappingModelList: [],
        ResourceFields: [],
        EnableSKU: false,
        DsaMappingId: null,
      } as MappingDetailsResponse;

      // Create a mock array of DynamicMappingFieldResponse with all required properties
      const fields: DynamicMappingFieldResponse[] = [
        {
          FormatFieldId: '1',
          FormatDataType: 'string',
          FormatField: 'name',
          Category: '',
          Path: '',
          UnitType: '',
          UnitCvl: '',
          UnitDefaultValue: '',
          Unique: false,
          Recommended: false,
          Mandatory: false,
          DefaultValue: '',
          MaxLength: null,
          Description: '',
          inRiverEntityTypeId: null,
          inRiverFieldTypeId: null,
          inRiverDataType: null,
          inRiverFieldType: null,
          ConverterArgs: null,
          ConverterClass: null,
          ConverterId: null,
          MinLength: null,
          MaxValue: '',
          MinValue: '',
          MaxInstances: 0,
          MinInstances: 0,
          DecimalFormat: '',
          RegEx: '',
          ConditionalRule: '',
          ChildAttributes: [],
          Format: '',
          GroupRequiredFields: '',
          Enumerations: [],
          EnumerationValues: '',
          CvlCompleteness: false,
        },
      ];

      const result = initDefaultDsaMapping(mapping, fields);

      expect(result.MappingName).toBe('dsa mapping for Test Mapping');
      expect(result.FirstRelatedEntityTypeId).toBe('entity1');
      expect(result.FirstLinkEntityTypeId).toBe('link1');
      expect(result.SecondRelatedEntityTypeId).toBe('entity2');
      expect(result.SecondLinkEntityTypeId).toBe('link2');
      expect(result.WorkareaEntityTypeId).toBe('workarea');
      expect(result.OutputEntityTypeId).toBe('output');
      expect(result.DefaultLanguage).toBe('sv');
      expect(result.MappingModelList).toEqual(fields);
      expect(result.DsaMappingId).toBe(null);
      expect(result.EnableSKU).toBe(false);
      expect(result.FormatId).toBe(456);
      expect(result.ImageUrl).toBe(null);
    });

    it('should use default language "en" when DefaultLanguage is not provided', () => {
      // Create a minimal mock MappingDetailsResponse with required properties
      const mapping = {
        MappingId: 123,
        MappingName: 'Test Mapping',
        FirstRelatedEntityTypeId: 'entity1',
        FirstLinkEntityTypeId: null,
        SecondRelatedEntityTypeId: 'entity2',
        SecondLinkEntityTypeId: null,
        WorkareaEntityTypeId: 'workarea',
        OutputEntityTypeId: 'output',
        DefaultLanguage: null,
        FormatId: 456,
        MappingModelList: [],
        ResourceFields: [],
        EnableSKU: false,
        DsaMappingId: null,
        ImageUrl: null,
      } as unknown as MappingDetailsResponse;

      const fields: DynamicMappingFieldResponse[] = [];

      const result = initDefaultDsaMapping(mapping, fields);

      expect(result.DefaultLanguage).toBe('en');
    });
  });

  describe('initCurrentDsaMapping', () => {
    it('should initialize current DSA mapping with given ID and mapping', () => {
      const mappingId = 789;

      const mapping = {
        MappingId: 123,
        MappingName: 'Test Mapping',
        FirstRelatedEntityTypeId: 'entity1',
        FirstLinkEntityTypeId: 'link1',
        SecondRelatedEntityTypeId: 'entity2',
        SecondLinkEntityTypeId: 'link2',
        WorkareaEntityTypeId: 'workarea',
        OutputEntityTypeId: 'output',
        DefaultLanguage: 'sv',
        MappingModelList: [],
        FormatId: 456,
        ImageUrl: null,
        DsaMappingId: 321,
        EnableSKU: true,
      } as unknown as DynamicMappingDetailsResponse;

      const result = initCurrentDsaMapping(mappingId, mapping);

      expect(result.MappingId).toBe(mappingId);
      expect(result.MappingName).toBe('Test Mapping');
      expect(result.FirstRelatedEntityTypeId).toBe('entity1');
      expect(result.FirstLinkEntityTypeId).toBe('link1');
      expect(result.SecondRelatedEntityTypeId).toBe('entity2');
      expect(result.SecondLinkEntityTypeId).toBe('link2');
      expect(result.WorkareaEntityTypeId).toBe('workarea');
      expect(result.OutputEntityTypeId).toBe('output');
      expect(result.DefaultLanguage).toBe('sv');
      expect(result.MappingModelList).toEqual([]);
      expect(result.DsaMappingId).toBe(null);
      expect(result.EnableSKU).toBe(false);
      expect(result.FormatId).toBe(456);
      expect(result.ImageUrl).toBe(null);
    });

    it('should use default language "en" when DefaultLanguage is not provided', () => {
      const mappingId = 789;

      const mapping = {
        MappingId: 123,
        MappingName: 'Test Mapping',
        FirstRelatedEntityTypeId: 'entity1',
        FirstLinkEntityTypeId: 'link1',
        SecondRelatedEntityTypeId: 'entity2',
        SecondLinkEntityTypeId: 'link2',
        WorkareaEntityTypeId: 'workarea',
        OutputEntityTypeId: 'output',
        DefaultLanguage: undefined,
        MappingModelList: [],
        FormatId: 456,
        ImageUrl: null,
        DsaMappingId: null,
        EnableSKU: false,
      } as unknown as DynamicMappingDetailsResponse;

      const result = initCurrentDsaMapping(mappingId, mapping);

      expect(result.DefaultLanguage).toBe('en');
    });

    it('should set ImageUrl to null when not provided', () => {
      const mappingId = 789;

      const mapping = {
        MappingId: 123,
        MappingName: 'Test Mapping',
        FirstRelatedEntityTypeId: 'entity1',
        FirstLinkEntityTypeId: 'link1',
        SecondRelatedEntityTypeId: 'entity2',
        SecondLinkEntityTypeId: 'link2',
        WorkareaEntityTypeId: 'workarea',
        OutputEntityTypeId: 'output',
        DefaultLanguage: 'en',
        MappingModelList: [],
        FormatId: 456,
        DsaMappingId: null,
        EnableSKU: false,
      } as unknown as DynamicMappingDetailsResponse;

      const result = initCurrentDsaMapping(mappingId, mapping);

      expect(result.ImageUrl).toBe(null);
    });
  });

  describe('toDsaMapping', () => {
    it('should convert DSA mapping response to DynamicMappingDetailsResponse', () => {
      // Create a mock with all required properties
      const dsaMappingResponse = {
        id: 789,
        data: JSON.stringify({
          MappingId: 123,
          MappingName: 'Test Mapping',
          FirstRelatedEntityTypeId: 'entity1',
          FirstLinkEntityTypeId: 'link1',
          SecondRelatedEntityTypeId: 'entity2',
          SecondLinkEntityTypeId: 'link2',
          WorkareaEntityTypeId: 'workarea',
          OutputEntityTypeId: 'output',
          DefaultLanguage: 'sv',
          MappingModelList: [],
          FormatId: 456,
          ImageUrl: null,
          DsaMappingId: null,
          EnableSKU: false,
        }),
        environmentGid: 'env1',
        createdBy: 'user1',
        createdDate: '2023-01-01',
        updatedBy: 'user1',
        updatedDate: '2023-01-01',
      } as DsaMappingResponse;

      const result = toDsaMapping(dsaMappingResponse);

      expect(result.MappingId).toBe(789);
      expect(result.MappingName).toBe('Test Mapping');
      expect(result.FirstRelatedEntityTypeId).toBe('entity1');
      expect(result.DefaultLanguage).toBe('sv');
      expect(result.MappingModelList).toEqual([]);
      expect(result.FormatId).toBe(456);
    });

    it('should return empty object if parsing fails', () => {
      const dsaMappingResponse = {
        id: 789,
        data: 'invalid JSON',
        environmentGid: 'env1',
        createdBy: 'user1',
        createdDate: '2023-01-01',
        updatedBy: 'user1',
        updatedDate: '2023-01-01',
      } as DsaMappingResponse;

      const result = toDsaMapping(dsaMappingResponse);

      expect(result).toEqual({});
    });
  });
});

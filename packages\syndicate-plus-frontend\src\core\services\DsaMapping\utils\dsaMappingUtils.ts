import { DynamicMappingDetailsResponse, DynamicMappingFieldResponse, MappingDetailsResponse } from '@core/interfaces';
import { DynamicFormat, DynamicFormatRow } from '@core/interfaces/Category';
import { DsaMappingResponse } from '@core/interfaces/DsaMapping';

export const toMappingFields = (format: DynamicFormat): DynamicMappingFieldResponse[] => {
  if (!format || !format.formats || !Array.isArray(format.formats)) {
    console.warn('Invalid format or format rows');
    return [];
  }

  return format.formats.map(createMappingRow);
};

export const createMappingRow = (formatRow: DynamicFormatRow): DynamicMappingFieldResponse => {
  return {
    // info about mapped source field
    inRiverEntityTypeId: null, // required nullable
    inRiverFieldTypeId: null, // required nullable
    inRiverDataType: null, // required nullable
    inRiverFieldType: null, // nullable
    // fields for functions
    ConverterArgs: null, // required nullable
    ConverterClass: null, // required nullable
    ConverterId: null, // required nullable
    // format file fields to mapping fields
    FormatFieldId: formatRow.id, // required nullable
    FormatDataType: formatRow.datatype, // required string
    FormatField: formatRow.field, // required string
    Category: formatRow.category ? formatRow.category : '', // required string
    Path: formatRow.path ? formatRow.path : null, // required nullable
    UnitType: formatRow.unitType ? formatRow.unitType : null, // required nullable
    UnitCvl: formatRow.unitCvl ? formatRow.unitCvl : null, // required nullable
    UnitDefaultValue: formatRow.unitDefaultValue ? formatRow.unitDefaultValue : null, // required nullable
    Unique: !!formatRow.unique, // required boolean
    Recommended: !!formatRow.recommended, // required boolean
    Mandatory: !!formatRow.mandatory, // required boolean
    DefaultValue: formatRow.defaultValue ? formatRow.defaultValue : null, // required nullable
    MaxLength: formatRow.maxLength ? formatRow.maxLength : null, // required nullable
    Description: formatRow.description ? formatRow.description : null, // required nullable
    MinLength: formatRow.minLength,
    MaxValue: formatRow.maxValue,
    MinValue: formatRow.minValue,
    MaxInstances: formatRow.maxInstances,
    MinInstances: formatRow.minInstances,
    DecimalFormat: formatRow.decimalFormat,
    RegEx: formatRow.regEx,
    ConditionalRule: formatRow.conditionalRule,
    ChildAttributes: formatRow.childAttributes,
    Format: formatRow.format,
    GroupRequiredFields: formatRow.groupRequiredFields,
    Enumerations: [],
    CvlCompleteness: false,
    EnumerationValues: '',
  } as DynamicMappingFieldResponse;
};

export const initDefaultDsaMapping = (
  mapping: MappingDetailsResponse | DynamicMappingDetailsResponse,
  fields: DynamicMappingFieldResponse[]
): DynamicMappingDetailsResponse => {
  return {
    MappingId: null as any,
    MappingName: 'dsa mapping for ' + mapping.MappingName,
    // init with the same values as the mapping
    FirstRelatedEntityTypeId: mapping.FirstRelatedEntityTypeId,
    FirstLinkEntityTypeId: mapping.FirstLinkEntityTypeId,
    SecondRelatedEntityTypeId: mapping.SecondRelatedEntityTypeId,
    SecondLinkEntityTypeId: mapping.SecondLinkEntityTypeId,
    WorkareaEntityTypeId: mapping.WorkareaEntityTypeId,
    OutputEntityTypeId: mapping.OutputEntityTypeId,
    // DefaultLanguage is not used in the dsa mapping, so we can set it to 'en' or any other default value
    DefaultLanguage: mapping.DefaultLanguage || 'en', // default to 'en' if not provided
    MappingModelList: fields,
    DsaMappingId: null, // not used
    EnableSKU: false, // default to false

    FormatId: mapping.FormatId, // required number
    ImageUrl: null, // is not used in the dsa mapping
  };
};

export const initCurrentDsaMapping = (
  mappingId: number,
  mapping: DynamicMappingDetailsResponse
): DynamicMappingDetailsResponse => {
  return {
    MappingId: mappingId,
    MappingName: mapping.MappingName,
    FirstRelatedEntityTypeId: mapping.FirstRelatedEntityTypeId,
    FirstLinkEntityTypeId: mapping.FirstLinkEntityTypeId,
    SecondRelatedEntityTypeId: mapping.SecondRelatedEntityTypeId,
    SecondLinkEntityTypeId: mapping.SecondLinkEntityTypeId,
    WorkareaEntityTypeId: mapping.WorkareaEntityTypeId,
    OutputEntityTypeId: mapping.OutputEntityTypeId,
    DefaultLanguage: mapping.DefaultLanguage || 'en',
    MappingModelList: mapping.MappingModelList,
    DsaMappingId: null, // not used
    EnableSKU: false,
    FormatId: mapping.FormatId,
    ImageUrl: mapping.ImageUrl || null,
  };
};

export const toDsaMapping = (dsaMappingResponse: DsaMappingResponse): DynamicMappingDetailsResponse => {
  try {
    const parsedData = JSON.parse(dsaMappingResponse.data) as DynamicMappingDetailsResponse;
    return initCurrentDsaMapping(dsaMappingResponse.id, parsedData);
  } catch (error) {
    console.error('Failed to parse data from dsa mapping:', error);
    return {} as DynamicMappingDetailsResponse;
  }
};

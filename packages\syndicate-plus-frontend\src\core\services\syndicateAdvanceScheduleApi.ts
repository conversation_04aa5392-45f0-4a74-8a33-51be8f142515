import { portalFetch, proxyPortalFetch } from '@utils';
import { AddSchedulePayload, AddScheduleResponse } from '@core/interfaces';

export const addSchedule = async (schedule: AddSchedulePayload): Promise<AddScheduleResponse | undefined> => {
  const url = '/api/syndicate-advance/schedule';
  const errorMessage = 'Could not add schedule';
  try {
    const response = import.meta.env.DEV
      ? await proxyPortalFetch(url, { method: 'POST', body: JSON.stringify(schedule) })
      : await portalFetch(url, { method: 'POST', body: JSON.stringify(schedule) });
    return response === null ? undefined : await response.json();
  } catch (error) {
    console.error(errorMessage, error);
    return undefined;
  }
};

import { setActive<PERSON>inia, createPinia } from 'pinia';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useEnabledApiStore } from './useEnabledApiStore';
import * as EnvironmentHelper from '@helpers/EnvironmentHelper';

const bestBuyId = 'best-buy';
describe('useEnabledApiStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  it(`returns ["${bestBuyId}"] for dev-projectboston-devqa`, () => {
    vi.spyOn(EnvironmentHelper, 'getEnvironmentGlobalId').mockReturnValue('dev-projectboston-devqa');
    const store = useEnabledApiStore();
    expect(store.getEnabledApis()).toEqual([bestBuyId]);
  });

  it(`returns ["${bestBuyId}"] for prod-marshallgroup-prod`, () => {
    vi.spyOn(EnvironmentHelper, 'getEnvironmentGlobalId').mockReturnValue('prod-marshallgroup-prod');
    const store = useEnabledApiStore();
    expect(store.getEnabledApis()).toEqual([bestBuyId]);
  });

  it('returns [] for other environments', () => {
    vi.spyOn(EnvironmentHelper, 'getEnvironmentGlobalId').mockReturnValue('some-other-env');
    const store = useEnabledApiStore();
    expect(store.getEnabledApis()).toEqual([]);
  });
});

import { defineStore } from 'pinia';
import { getEnvironmentGlobalId } from '@helpers/EnvironmentHelper';

export const useEnabledApiStore = defineStore('coreEnabledApiStore', () => {
  const bestBuyEnvironments = ['dev-projectboston-devqa', 'prod-marshallgroup-prod'];

  // Functions
  const getEnabledApis = (): string[] => {
    const environmentGid = getEnvironmentGlobalId();

    if (bestBuyEnvironments.includes(environmentGid)) {
      return ['best-buy'];
    }
    return [];
  };

  return {
    getEnabledApis,
  };
});

import { vi, beforeEach, describe, it, expect, Mock } from 'vitest';
import { createP<PERSON>, setActivePinia } from 'pinia';
import { useDataSubmissionStore } from './useDataSubmissionStore';
import { fetchDataSubmissions, fetchDataSubmissionEntities } from '@core/services/dataSubmissionsApi';
import { JobState } from '@core/enums';

vi.mock('@core/services/dataSubmissionsApi');

describe('useDataSubmissionStore', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    setActivePinia(createPinia());
  });

  describe('loadDataSubmissions', () => {
    it('should load initial data submissions and update state correctly', async () => {
      // Arrange
      const store = useDataSubmissionStore();
      const mockResponse = {
        dataSubmissions: [
          {
            id: 1,
            jobId: 101,
            environmentGid: 'env-123',
            tradingPartnerId: 'partner-1',
            state: JobState.SUCCESS,
            createdDate: '2023-01-01T00:00:00Z',
            updatedDate: '2023-01-01T00:00:00Z',
          },
          {
            id: 2,
            jobId: 101,
            environmentGid: 'env-123',
            tradingPartnerId: 'partner-1',
            state: JobState.SUCCESS,
            createdDate: '2023-01-01T00:00:00Z',
            updatedDate: '2023-01-01T00:00:00Z',
          },
        ],
        totalCount: 2,
        page: 1,
        pageSize: 100,
      };
      (fetchDataSubmissions as Mock).mockResolvedValue(mockResponse);

      // Act
      await store.loadDataSubmissions();

      // Assert
      expect(fetchDataSubmissions).toHaveBeenCalledWith(1, 100, undefined);
      expect(store.dataSubmissions).toEqual(mockResponse.dataSubmissions);
      expect(store.submissionsPage).toBe(1);
      expect(store.isLastPage).toBe(true);
    });

    it('should set isLastPage to true when fewer items returned than pageSize', async () => {
      // Arrange
      const store = useDataSubmissionStore();
      const mockResponse = {
        dataSubmissions: [{ id: 1, name: 'Submission 1' }],
        totalCount: 1,
        page: 1,
        pageSize: 100,
      };
      (fetchDataSubmissions as Mock).mockResolvedValue(mockResponse);

      // Act
      await store.loadDataSubmissions();

      // Assert
      expect(store.isLastPage).toBe(true);
    });

    it('should append new submissions when loading subsequent pages', async () => {
      // Arrange
      const store = useDataSubmissionStore();
      const initialSubmissions = [
        {
          id: 1,
          jobId: 101,
          environmentGid: 'env-123',
          tradingPartnerId: 'partner-1',
          state: JobState.SUCCESS,
          createdDate: '2023-01-01T00:00:00Z',
          updatedDate: '2023-01-01T00:00:00Z',
        },
      ];
      const newSubmissions = [
        {
          id: 2,
          jobId: 101,
          environmentGid: 'env-123',
          tradingPartnerId: 'partner-1',
          state: JobState.SUCCESS,
          createdDate: '2023-01-01T00:00:00Z',
          updatedDate: '2023-01-01T00:00:00Z',
        },
      ];

      // Set initial state
      store.dataSubmissions = initialSubmissions;
      store.submissionsPage = 2;

      // Mock second page response
      const mockResponse = {
        dataSubmissions: newSubmissions,
        totalCount: 2,
        page: 2,
        pageSize: 100,
      };
      (fetchDataSubmissions as Mock).mockResolvedValue(mockResponse);

      // Act
      await store.loadDataSubmissions();

      // Assert
      expect(fetchDataSubmissions).toHaveBeenCalledWith(2, 100, undefined);
      expect(store.dataSubmissions).toEqual([...initialSubmissions, ...newSubmissions]);
      expect(store.submissionsPage).toBe(2); // Page doesn't increment further than 2
    });

    it('should pass jobId parameter when provided', async () => {
      // Arrange
      const store = useDataSubmissionStore();
      const jobId = 123;
      const mockResponse = {
        dataSubmissions: [{ id: 1, name: 'Submission 1' }],
        totalCount: 1,
        page: 1,
        pageSize: 100,
      };
      (fetchDataSubmissions as Mock).mockResolvedValue(mockResponse);

      // Act
      await store.loadDataSubmissions(jobId);

      // Assert
      expect(fetchDataSubmissions).toHaveBeenCalledWith(1, 100, jobId);
    });

    it('should not fetch data if isLoadingSubmissions is true', async () => {
      // Arrange
      const store = useDataSubmissionStore();
      store.isLoadingSubmissions = true;

      // Act
      await store.loadDataSubmissions();

      // Assert
      expect(fetchDataSubmissions).not.toHaveBeenCalled();
    });

    it('should not fetch data if isLastPage is true', async () => {
      // Arrange
      const store = useDataSubmissionStore();
      store.isLastPage = true;

      // Act
      await store.loadDataSubmissions();

      // Assert
      expect(fetchDataSubmissions).not.toHaveBeenCalled();
    });
  });

  describe('loadDataSubmissionEntities', () => {
    it('should load initial entities', async () => {
      // Arrange
      const store = useDataSubmissionStore();
      const dataSubmissionId = 1;
      // Setup a mock submission with PENDING state
      store.dataSubmissions = [
        {
          id: 1,
          jobId: 101,
          environmentGid: 'env-123',
          tradingPartnerId: 'partner-1',
          state: JobState.PENDING,
          createdDate: '2023-01-01T00:00:00Z',
          updatedDate: '2023-01-01T00:00:00Z',
        },
      ];

      const mockResponse = {
        entities: [
          { id: 1, dataSubmissionId: 1 },
          { id: 2, dataSubmissionId: 1 },
        ],
        totalCount: 2,
        page: 1,
        pageSize: 50,
      };
      (fetchDataSubmissionEntities as Mock).mockResolvedValue(mockResponse);

      // Act
      await store.loadDataSubmissionEntities(dataSubmissionId);

      // Assert
      expect(fetchDataSubmissionEntities).toHaveBeenCalledWith(dataSubmissionId, 1, 50);
      expect(store.dataSubmissionEntities).toEqual(mockResponse.entities);
      expect(store.entitiesPage).toBe(1); // Page remains 1 after loading initial entities
      expect(store.isLastEntitiesPage).toBe(true); // isLastEntitiesPage is true for initial load
    });

    it('should set isLastEntitiesPage to true when fewer items returned than pageSize', async () => {
      // Arrange
      const store = useDataSubmissionStore();
      const dataSubmissionId = 1;
      // Set up a mock submission
      store.dataSubmissions = [
        {
          id: 1,
          jobId: 101,
          environmentGid: 'env-123',
          tradingPartnerId: 'partner-1',
          state: JobState.SUCCESS,
          createdDate: '2023-01-01T00:00:00Z',
          updatedDate: '2023-01-01T00:00:00Z',
        },
      ];
      const mockResponse = {
        entities: [{ id: 1, dataSubmissionId: 1 }],
        totalCount: 1,
        page: 1,
        pageSize: 50,
      };
      (fetchDataSubmissionEntities as Mock).mockResolvedValue(mockResponse);

      // Act
      await store.loadDataSubmissionEntities(dataSubmissionId);

      // Assert
      expect(store.isLastEntitiesPage).toBe(true);
    });

    it('should reset entities when isDataSubmissionIdChanged is true', async () => {
      // Arrange
      const store = useDataSubmissionStore();
      const dataSubmissionId = 1;
      const isDataSubmissionIdChanged = true;

      // Set up a mock submission
      store.dataSubmissions = [
        {
          id: 1,
          jobId: 101,
          environmentGid: 'env-123',
          tradingPartnerId: 'partner-1',
          state: JobState.SUCCESS,
          createdDate: '2023-01-01T00:00:00Z',
          updatedDate: '2023-01-01T00:00:00Z',
        },
      ];

      // Set initial state
      store.dataSubmissionEntities = [
        {
          id: 99,
          createdDate: '2023-01-01T00:00:00Z',
          updatedDate: '2023-01-01T00:00:00Z',
          dataSubmissionId: 2,
          entityId: 200,
          correlationId: 'corr-99',
          state: JobState.SUCCESS,
        },
      ];
      store.entitiesPage = 3;
      store.isLastEntitiesPage = true;

      const mockResponse = {
        entities: [{ id: 1, dataSubmissionId: 1 }],
        totalCount: 1,
        page: 1,
        pageSize: 50,
      };
      (fetchDataSubmissionEntities as Mock).mockResolvedValue(mockResponse);

      // Act
      await store.loadDataSubmissionEntities(dataSubmissionId, isDataSubmissionIdChanged);

      // Assert
      expect(store.entitiesPage).toBe(1); // Should reset to 1
      expect(store.isLastEntitiesPage).toBe(true);
      expect(store.dataSubmissionEntities).toEqual(mockResponse.entities);
    });

    it('should append new entities when loading subsequent pages', async () => {
      // Arrange
      const store = useDataSubmissionStore();
      const dataSubmissionId = 1;
      // Setup a mock submission with SUCCESS state
      store.dataSubmissions = [
        {
          id: 1,
          jobId: 101,
          environmentGid: 'env-123',
          tradingPartnerId: 'partner-1',
          state: JobState.SUCCESS,
          createdDate: '2023-01-01T00:00:00Z',
          updatedDate: '2023-01-01T00:00:00Z',
        },
      ];

      const initialEntities = [
        {
          id: 1,
          createdDate: '2023-01-01T00:00:00Z',
          updatedDate: '2023-01-01T00:00:00Z',
          dataSubmissionId: 1,
          entityId: 100,
          correlationId: 'corr-1',
          state: JobState.SUCCESS,
        },
      ];
      const newEntities = [
        {
          id: 2,
          createdDate: '2023-01-02T00:00:00Z',
          updatedDate: '2023-01-02T00:00:00Z',
          dataSubmissionId: 1,
          entityId: 101,
          correlationId: 'corr-2',
          state: JobState.SUCCESS,
        },
      ];

      // Set initial state
      store.dataSubmissionEntities = initialEntities;
      store.entitiesPage = 2;
      store.isLoadingEntities = false;

      // Mock second page response
      const mockResponse = {
        entities: newEntities,
        totalCount: 2,
        page: 2,
        pageSize: 50,
      };
      (fetchDataSubmissionEntities as Mock).mockResolvedValue(mockResponse);

      // Act
      await store.loadDataSubmissionEntities(dataSubmissionId);

      // Assert
      expect(fetchDataSubmissionEntities).toHaveBeenCalledWith(dataSubmissionId, 2, 50);
      expect(store.dataSubmissionEntities).toEqual([...initialEntities, ...newEntities]);
      expect(store.entitiesPage).toBe(2);
    });

    it('should not fetch data if isLoadingEntities is true', async () => {
      // Arrange
      const store = useDataSubmissionStore();
      store.isLoadingEntities = true;

      // Act
      await store.loadDataSubmissionEntities(1);

      // Assert
      expect(fetchDataSubmissionEntities).not.toHaveBeenCalled();
    });

    it('should not fetch data if no dataSubmissionId is provided', async () => {
      // Arrange
      const store = useDataSubmissionStore();
      const dataSubmissionId = 0; // falsy value

      // Act
      await store.loadDataSubmissionEntities(dataSubmissionId);

      // Assert
      expect(fetchDataSubmissionEntities).not.toHaveBeenCalled();
    });
  });
  describe('selectSubmission', () => {
    it('should set selectedSubmissionId and load entities', async () => {
      // Arrange
      const store = useDataSubmissionStore();
      const dataSubmissionId = 5;

      // Setup a mock submission with FAILED state
      store.dataSubmissions = [
        {
          id: 5,
          jobId: 101,
          environmentGid: 'env-123',
          tradingPartnerId: 'partner-1',
          state: JobState.FAILED,
          createdDate: '2023-01-01T00:00:00Z',
          updatedDate: '2023-01-01T00:00:00Z',
        },
      ];

      // Set initial state to make sure clearing works
      store.dataSubmissionEntities = [
        {
          id: 2,
          createdDate: '2023-01-02T00:00:00Z',
          updatedDate: '2023-01-02T00:00:00Z',
          dataSubmissionId: 1,
          entityId: 101,
          correlationId: 'corr-2',
          state: JobState.SUCCESS,
        },
      ];

      (fetchDataSubmissionEntities as Mock).mockResolvedValue({
        entities: [],
        totalCount: 0,
        page: 1,
        pageSize: 50,
      });

      // Act
      await store.selectSubmission(dataSubmissionId);

      // Assert
      expect(store.selectedSubmissionId).toBe(dataSubmissionId);
      expect(store.entitiesPage).toBe(1); // Should be reset to 1 when selectSubmission is called
      expect(store.dataSubmissionEntities).toEqual([]);
      expect(fetchDataSubmissionEntities).toHaveBeenCalledWith(dataSubmissionId, 1, 50);
    });
  });

  describe('resetState', () => {
    it('should reset all state properties to their initial values', () => {
      // Arrange
      const store = useDataSubmissionStore();

      // Set state to non-default values
      store.dataSubmissions = [
        {
          id: 1,
          jobId: 101,
          environmentGid: 'env-123',
          tradingPartnerId: 'partner-1',
          state: JobState.SUCCESS,
          createdDate: '2023-01-01T00:00:00Z',
          updatedDate: '2023-01-01T00:00:00Z',
        },
      ];
      store.submissionsPage = 3;
      store.isLastPage = true;
      store.isLoadingSubmissions = true;

      store.dataSubmissionEntities = [
        {
          id: 1,
          createdDate: '2023-01-01T00:00:00Z',
          updatedDate: '2023-01-01T00:00:00Z',
          dataSubmissionId: 1,
          entityId: 100,
          correlationId: 'corr-1',
          state: JobState.SUCCESS,
        },
      ];
      store.entitiesPage = 4;
      store.isLastEntitiesPage = true;
      store.isLoadingEntities = true;
      store.selectedSubmissionId = 5;

      // Act
      store.resetState();

      // Assert
      expect(store.dataSubmissions).toEqual([]);
      expect(store.submissionsPage).toBe(1);
      expect(store.isLastPage).toBe(false);

      expect(store.dataSubmissionEntities).toEqual([]);
      expect(store.entitiesPage).toBe(1);
      expect(store.isLastEntitiesPage).toBe(false);
      expect(store.selectedSubmissionId).toBe(null);

      // Loading flags should not be reset by resetState
      expect(store.isLoadingSubmissions).toBe(true);
      expect(store.isLoadingEntities).toBe(true);
    });
  });
});

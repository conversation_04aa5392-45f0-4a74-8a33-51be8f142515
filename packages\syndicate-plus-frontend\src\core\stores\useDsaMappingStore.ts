import { defineStore } from 'pinia';
import { ref } from 'vue';
import {
  deleteDsaMapping,
  fetchDsaMappingById,
  getDefaultDsaMappingFields,
  saveDsaMapping,
  updateDsaMapping,
} from '@core/services/DsaMapping';
import { initDefaultDsaMapping } from '@core/services/DsaMapping/utils';
import { DynamicMappingDetailsResponse, DynamicMappingFieldResponse, MappingDetailsResponse } from '@core/interfaces';

export const useDsaMappingStore = defineStore('dsaMappingStore', () => {
  // Refs
  const isLoading = ref(false);
  const dsaMapping = ref<DynamicMappingDetailsResponse | null>(null);
  const defaultDsaMapping = ref<DynamicMappingDetailsResponse | null>(null);

  // Functions
  const setDefaultDsaMapping = () => {
    defaultDsaMapping.value = JSON.parse(JSON.stringify(dsaMapping.value));
  };

  const hasUnsavedChanges = (): boolean => {
    if (!dsaMapping.value || !defaultDsaMapping.value) {
      return false;
    }

    return JSON.stringify(dsaMapping.value) !== JSON.stringify(defaultDsaMapping.value);
  };

  const initializeDsaMapping = async (
    mapping: MappingDetailsResponse | DynamicMappingDetailsResponse,
    dsaMappingId: number | null = null
  ) => {
    isLoading.value = true;
    try {
      if (!dsaMappingId) {
        // Init default dsa mapping with default fields
        const defaultMappingFields = await getDefaultDsaMappingFields();
        dsaMapping.value = initDefaultDsaMapping(mapping, defaultMappingFields);
        setDefaultDsaMapping();
        isLoading.value = false;
        return;
      }

      dsaMapping.value = await fetchDsaMappingById(dsaMappingId);
      setDefaultDsaMapping();
    } catch (error) {
      console.error('Error initializing DSA mapping:', error);
      isLoading.value = false;
      return;
    }

    isLoading.value = false;
  };

  const mapAutomatically = () => {
    console.log('Mapping DSA fields automatically');
  };

  const unmapSourceField = (field: DynamicMappingFieldResponse) => {
    console.log('Unmapping source field:', field);
  };

  const saveNewDsaMapping = async (): Promise<number | null> => {
    if (!dsaMapping.value) {
      console.error('No DSA mapping to save');
      return null;
    }

    isLoading.value = true;
    try {
      const dsaMappingResponse = await saveDsaMapping(dsaMapping.value);
      dsaMapping.value.MappingId = dsaMappingResponse.id;
      setDefaultDsaMapping();
      isLoading.value = false;

      return dsaMappingResponse.id || null;
    } catch (error) {
      console.error('Error saving DSA mapping:', error);
      isLoading.value = false;
      return null;
    }
  };

  const updateExistingDsaMapping = async (): Promise<boolean> => {
    if (!dsaMapping.value) {
      console.error('No DSA mapping to update');
      return false;
    }

    isLoading.value = true;
    try {
      const success = await updateDsaMapping(dsaMapping.value);
      if (success) {
        // After successful update, update the default reference to match current state
        setDefaultDsaMapping();
      }
      isLoading.value = false;
      return success;
    } catch (error) {
      console.error('Error updating DSA mapping:', error);
      isLoading.value = false;
      return false;
    }
  };

  const deleteDsaMappingById = async (dsaMappingId: number): Promise<boolean> => {
    if (!dsaMappingId) {
      console.error('No DSA mapping ID provided for deletion');
      return false;
    }

    isLoading.value = true;
    try {
      const success = await deleteDsaMapping(dsaMappingId);
      isLoading.value = false;
      return success;
    } catch (error) {
      console.error('Error deleting DSA mapping:', error);
      isLoading.value = false;
      return false;
    }
  };

  const resetDsaMapping = () => {
    dsaMapping.value = JSON.parse(JSON.stringify(defaultDsaMapping.value));
  };

  const clearStore = () => {
    dsaMapping.value = null;
    defaultDsaMapping.value = null;
    isLoading.value = false;
  };

  // Return
  return {
    isLoading,
    dsaMapping,
    hasUnsavedChanges,
    initializeDsaMapping,
    mapAutomatically,
    unmapSourceField,
    saveNewDsaMapping,
    updateExistingDsaMapping,
    deleteDsaMappingById,
    resetDsaMapping,
    clearStore,
  };
});

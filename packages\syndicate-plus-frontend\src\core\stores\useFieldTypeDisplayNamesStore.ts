import { defineStore } from 'pinia';
import { ref } from 'vue';
import { InriverFieldTypeResponse } from '@core/interfaces';
import { getFieldTypes } from '@core/services/FieldTypes';

export interface EntityFieldTypesCacheModel {
  [entityTypeId: string]: FieldTypeShortModel[];
}

export interface FieldTypeShortModel {
  fieldTypeId: string;
  displayName?: string;
}

export const useFieldTypeDisplayNamesStore = defineStore('fieldTypeDisplayNamesStore', () => {
  // Refs
  const mappingFieldTypesCache = ref<EntityFieldTypesCacheModel>({});

  // Functions
  const loadFieldTypeDisplayNames = async (
    entityTypeIds: string[]
  ): Promise<EntityFieldTypesCacheModel | undefined> => {
    if (!entityTypeIds?.length) {
      return;
    }

    for (const entityTypeId of entityTypeIds) {
      if (mappingFieldTypesCache.value?.[entityTypeId]) {
        continue;
      }

      const fields = [] as FieldTypeShortModel[];
      const entityTypeFields = await getFieldTypes(entityTypeId);
      entityTypeFields.forEach((field: InriverFieldTypeResponse) => {
        fields.push({
          fieldTypeId: field.id,
          displayName: field.displayname,
        } as FieldTypeShortModel);
      });
      mappingFieldTypesCache.value[entityTypeId] = fields;
    }

    return mappingFieldTypesCache.value;
  };

  const clearStore = () => {
    mappingFieldTypesCache.value = {};
  };

  return {
    loadFieldTypeDisplayNames,
    mappingFieldTypesCache,
    clearStore,
  };
});

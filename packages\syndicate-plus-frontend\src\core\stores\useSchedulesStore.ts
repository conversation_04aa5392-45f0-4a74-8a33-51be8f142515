import { ref } from 'vue';
import { defineStore } from 'pinia';
import { Schedule } from '@core/interfaces';
import { deleteSchedule, getSchedulesByTargetCompany } from '@core/services';

export const useSchedulesStore = defineStore('schedulesStore', () => {
  // Refs
  const currentSchedules = ref<Schedule[]>([]);
  const isLoading = ref(false);
  const limit = ref<number>(30);
  const offset = ref<number>(0);
  const isLastPage = ref<boolean>(false);

  // Functions
  async function fetchSchedulesByTradingPartnerId(tradingPartnerId: string) {
    isLoading.value = true;

    try {
      const schedules = await getSchedulesByTargetCompany(tradingPartnerId, offset.value, limit.value);
      if (schedules.length < limit.value) {
        isLastPage.value = true;
      } else {
        offset.value++;
      }

      currentSchedules.value = [...currentSchedules.value, ...schedules];
    } finally {
      isLoading.value = false;
    }
  }

  const fetch = async (tradingPartnerId: string, clear?: boolean): Promise<void> => {
    clear && clearStore();
    if (isLoading.value || isLastPage.value) {
      return;
    }

    await fetchSchedulesByTradingPartnerId(tradingPartnerId);
  };

  const deleteScheduleById = async (scheduleId: number): Promise<boolean> => {
    isLoading.value = true;
    try {
      return await deleteSchedule(scheduleId);
    } finally {
      isLoading.value = false;
    }
  };

  const clearStore = () => {
    currentSchedules.value = [];
    isLoading.value = false;
    offset.value = 0;
    isLastPage.value = false;
  };

  return {
    currentSchedules,
    isLoading,
    isLastPage,
    fetch,
    deleteScheduleById,
    clearStore,
  };
});

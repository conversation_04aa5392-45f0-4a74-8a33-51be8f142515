/* Microfrontend CSS Isolation - HIGH SPECIFICITY for HInclude Load Order */

/* Use maximum specificity to ensure our styles always win regardless of load order */
.syndicate-plus-isolated.syndicate-plus-isolated.syndicate-plus-isolated {
  /* Container isolation - minimal approach to avoid position:fixed issues */
  isolation: isolate;
  position: relative;
  z-index: 1;

  /* Reset inherited styles that might conflict */
  font-family: Wallop, Roboto, -apple-system, 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: inherit;
  background: transparent;

  /* Prevent parent styles from bleeding in */
  box-sizing: border-box;

  /* Ensure all child elements use border-box */
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }

  /* Minimal Quasar component fixes - only override what conflicts */
  .q-btn.q-btn {
    font-family: inherit !important;
    text-transform: none !important;
  }

  .q-tab.q-tab {
    font-family: inherit !important;
    text-transform: none !important;
  }

  .q-table.q-table {
    font-family: inherit !important;

    th,
    td {
      font-family: inherit !important;
    }
  }

  .q-field.q-field {
    font-family: inherit !important;

    .q-field__control {
      font-family: inherit !important;
    }
  }

  /* Toast notifications - ensure they appear above everything */
  .v-toast {
    z-index: 9999 !important;
    position: fixed !important;

    &__item {
      font-family: inherit;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      position: relative;
      z-index: 9999;
    }
  }

  /* High specificity h3 styles to override external portal styles */
  h3,
  h3.dialog-title,
  .dialog-title h3,
  h3[data-id='dialog-title'],
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    &:is(h3) {
      font-size: 18px !important;
      font-weight: 600 !important;
    }
  }

  /* Additional fallback with even higher specificity */
  h3 {
    font-size: 18px !important;
    font-weight: 600 !important;
  }
}

/* HInclude-specific overrides */
@media screen {
  .syndicate-plus-isolated {
    /* Ensure the app doesn't break portal layout */
    max-width: 100%;
    overflow: visible;

    /* Prevent margin/padding conflicts */
    margin: 0;
    padding: 0;
  }
}

/* Global h3 override for HInclude environments - maximum specificity */
.syndicate-plus-isolated.syndicate-plus-isolated.syndicate-plus-isolated h3,
.syndicate-plus-isolated.syndicate-plus-isolated h3,
.syndicate-plus-isolated h3,
.syndicate-plus-isolated h3[data-id='dialog-title'],
.syndicate-plus-isolated .c-dialog h3[data-id='dialog-title'],
h3.dialog-title,
.dialog-title h3,
h3[data-id='dialog-title'] {
  font-size: 18px !important;
  font-weight: 600 !important;
}

/* Minimal conflict resolution - only override what actually conflicts */

.syndicate-plus-isolated.syndicate-plus-isolated.syndicate-plus-isolated {
  /* Set a consistent font family for the entire container */
  font-family: Wallop, Roboto, -apple-system, 'Helvetica Neue', Helvetica, Arial, sans-serif !important;

  /* Only override the specific properties that conflict with header fragment */

  /* Fix button conflicts */
  .q-btn {
    font-family: inherit !important;
    text-transform: none !important;
  }

  /* Fix tab conflicts */
  .q-tab {
    font-family: inherit !important;
    text-transform: none !important;
  }

  /* Fix table conflicts */
  .q-table {
    font-family: inherit !important;

    th,
    td {
      font-family: inherit !important;
    }
  }

  /* Fix form field conflicts */
  .q-field {
    font-family: inherit !important;

    .q-field__control {
      font-family: inherit !important;
    }
  }

  /* Ensure toast notifications appear above everything */
  .v-toast {
    z-index: 9999 !important;
    font-family: inherit !important;
  }
}

import { Locator, <PERSON> } from '@playwright/test';
import { BasePage } from '@pages/basePage.page';
import { ScheduledRunConfigurationPage } from './scheduledRunConfiguration.page';

export class RunSyndicationDialogPage extends BasePage {
  private page: Page;
  readonly dialog: Locator;
  readonly dialogTitle: Locator;
  readonly outputSelect: Locator;
  readonly scheduleCheckbox: Locator;
  readonly confirmButton: Locator;
  readonly cancelButton: Locator;
  readonly scheduledRunConfiguration: ScheduledRunConfigurationPage;
  readonly selectFromList = (text: string): Locator => this.page.getByRole('option', { name: text, exact: true });

  constructor(page: Page) {
    super(page);
    this.page = page;
    this.dialog = page.locator('.q-dialog');
    this.dialogTitle = page.locator('.q-dialog .q-card__section h6');
    this.outputSelect = page.locator('.q-dialog [placeholder="select output"]');
    this.scheduleCheckbox = page.locator('.q-checkbox').filter({ hasText: 'scheduled' });
    this.confirmButton = page.locator('.q-dialog .q-btn:has-text("save")');
    this.cancelButton = page.getByRole('button', { name: 'cancel', exact: true });
    this.scheduledRunConfiguration = new ScheduledRunConfigurationPage(page);
  }

  async selectOutput(outputName: string): Promise<void> {
    try {
      // Wait for the output select to be visible and click it
      await this.outputSelect.waitFor({ state: 'visible', timeout: 10000 });
      await this.outputSelect.click();

      // Wait for the dropdown to appear and select the output
      const option = this.selectFromList(outputName);
      await option.waitFor({ state: 'visible', timeout: 10000 });
      await option.click();

      // Verify selection was made by waiting for the dropdown to close
      await this.page.waitForTimeout(500);
    } catch (error) {
      console.error(`Failed to select output "${outputName}":`, error);
      throw error;
    }
  }

  async enableSchedule(): Promise<void> {
    try {
      await this.scheduleCheckbox.waitFor({ state: 'visible', timeout: 10000 });
      const isChecked = await this.scheduleCheckbox.isChecked();
      if (!isChecked) {
        await this.scheduleCheckbox.click();
        // Wait for the scheduled configuration to become visible after clicking
        await this.scheduledRunConfiguration.scheduleNameInput.waitFor({ state: 'visible', timeout: 10000 });
      }
    } catch (error) {
      console.error('Failed to enable schedule:', error);
      throw error;
    }
  }

  async disableSchedule(): Promise<void> {
    await this.scheduleCheckbox.waitFor({ state: 'visible' });
    const isChecked = await this.scheduleCheckbox.isChecked();
    if (isChecked) {
      await this.scheduleCheckbox.click();
      // Wait for the scheduled configuration to become hidden after clicking
      await this.scheduledRunConfiguration.scheduleNameInput.waitFor({ state: 'hidden' });
    }
  }

  async confirm(): Promise<void> {
    try {
      const saveButton = this.page.locator('.q-dialog .q-btn:has-text("save")');
      await saveButton.waitFor({ state: 'visible', timeout: 10000 });
      await saveButton.click({ force: true });
    } catch (error) {
      console.error('Failed to confirm dialog:', error);
      throw error;
    }
  }

  async cancel(): Promise<void> {
    try {
      await this.cancelButton.waitFor({ state: 'visible', timeout: 10000 });
      await this.cancelButton.click({ force: true });
    } catch (error) {
      console.error('Failed to cancel dialog:', error);
      throw error;
    }
  }

  async createScheduledSyndication(
    scheduleName: string,
    frequency: 'daily' | 'weekly' | 'monthly' | 'once',
    options?: {
      days?: string[];
      time?: string;
      dateTime?: string;
      outputName?: string;
    }
  ): Promise<void> {
    // Configure schedule
    await this.scheduledRunConfiguration.fillScheduleName(scheduleName);
    await this.scheduledRunConfiguration.selectFrequency(frequency);

    // Set additional options based on frequency
    if (frequency === 'weekly' && options?.days) {
      await this.scheduledRunConfiguration.selectDays(options.days);
    }

    if (options?.time && (frequency === 'daily' || frequency === 'weekly')) {
      await this.scheduledRunConfiguration.setTime(options.time);
    }

    if (options?.dateTime && (frequency === 'once' || frequency === 'monthly')) {
      await this.scheduledRunConfiguration.setDateTime(options.dateTime);
    }

    // Confirm
    await this.confirm();
  }

  async waitForDialog(): Promise<void> {
    await this.dialog.waitFor({ state: 'visible', timeout: 15000 });
  }

  async waitForDialogToClose(): Promise<void> {
    await this.dialog.waitFor({ state: 'hidden', timeout: 15000 });
  }
}

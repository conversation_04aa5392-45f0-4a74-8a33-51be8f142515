import { Locator, Page } from '@playwright/test';
import { BasePage } from '@pages/basePage.page';

export class ScheduledRunConfigurationPage extends BasePage {
  private page: Page;
  readonly scheduleNameInput: Locator;
  readonly dailyButton: Locator;
  readonly weeklyButton: Locator;
  readonly monthlyButton: Locator;
  readonly onceButton: Locator;
  readonly startDateButton: Locator;
  readonly endDateButton: Locator;
  readonly timeInput: Locator;
  readonly dateTimeInput: Locator;
  readonly calendarIcon: Locator;
  readonly dateDialog: Locator;
  readonly timeDialog: Locator;
  readonly mondayButton: Locator;
  readonly tuesdayButton: Locator;
  readonly wednesdayButton: Locator;
  readonly thursdayButton: Locator;
  readonly fridayButton: Locator;
  readonly saturdayButton: Locator;
  readonly sundayButton: Locator;
  readonly daysContainer: Locator;

  constructor(page: Page) {
    super(page);
    this.page = page;
    this.scheduleNameInput = page.getByRole('textbox', { name: 'name your syndication schedule' });
    this.dailyButton = page.getByText('daily');
    this.weeklyButton = page.getByText('weekly');
    this.monthlyButton = page.getByText('monthly');
    this.onceButton = page.getByText('once');
    this.startDateButton = page.locator('input[mask]');
    this.endDateButton = page.locator('input[mask]').nth(1);
    this.timeInput = page.getByRole('textbox', { name: 'execution time' });
    this.dateTimeInput = page.getByTestId('scheduled-run-configuration-date-time-input');
    this.calendarIcon = page.locator('.q-icon.mdi-calendar-outline');
    this.dateDialog = page.locator('.q-date');
    this.timeDialog = page.locator('.q-time');
    this.mondayButton = page.locator('.days .q-btn').nth(0);
    this.tuesdayButton = page.locator('.days .q-btn').nth(1);
    this.wednesdayButton = page.locator('.days .q-btn').nth(2);
    this.thursdayButton = page.locator('.days .q-btn').nth(3);
    this.fridayButton = page.locator('.days .q-btn').nth(4);
    this.saturdayButton = page.locator('.days .q-btn').nth(5);
    this.sundayButton = page.locator('.days .q-btn').nth(6);
    this.daysContainer = page.locator('.days');
  }

  async fillScheduleName(name: string): Promise<void> {
    await this.scheduleNameInput.fill(name);
  }

  async selectFrequency(frequency: 'daily' | 'weekly' | 'monthly' | 'once'): Promise<void> {
    switch (frequency) {
      case 'daily':
        await this.dailyButton.click();
        break;
      case 'weekly':
        await this.weeklyButton.click();
        break;
      case 'monthly':
        await this.monthlyButton.click();
        break;
      case 'once':
        await this.onceButton.click();
        break;
    }
  }

  async selectDays(days: string[]): Promise<void> {
    const dayButtons = {
      monday: this.mondayButton,
      tuesday: this.tuesdayButton,
      wednesday: this.wednesdayButton,
      thursday: this.thursdayButton,
      friday: this.fridayButton,
      saturday: this.saturdayButton,
      sunday: this.sundayButton,
    };

    for (const day of days) {
      const button = dayButtons[day.toLowerCase() as keyof typeof dayButtons];
      if (button) {
        await button.click();
      }
    }
  }

  async setTime(time: string): Promise<void> {
    await this.timeInput.fill(time);
  }

  async setDateTime(dateTime: string): Promise<void> {
    await this.dateTimeInput.fill(dateTime);
  }
}

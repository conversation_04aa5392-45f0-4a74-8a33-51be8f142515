import { Locator, Page } from '@playwright/test';
import { BasePage } from '@pages/basePage.page';

export class SchedulesTabPage extends BasePage {
  private page: Page;
  readonly schedulesTable: Locator;
  readonly schedulesTableRows: Locator;
  private deleteButton: Locator;
  readonly noDataContainer: Locator;
  readonly noDataTitle: Locator;
  readonly noDataMessage: Locator;
  readonly loadingSpinner: Locator;

  constructor(page: Page) {
    super(page);
    this.page = page;
    this.schedulesTable = page.locator('.schedules-table');
    this.schedulesTableRows = page.locator('.q-virtual-scroll__content tr.cursor-pointer');
    this.deleteButton = page.locator('button[aria-label*="delete"]');
    this.noDataContainer = page.locator('.no-data-table');
    this.noDataTitle = page.locator('.no-data-table .title');
    this.noDataMessage = page.locator('.no-data-table .text');
    this.loadingSpinner = page.locator('.q-inner-loading');
  }

  async selectSchedule(scheduleName: string): Promise<void> {
    const scheduleRow = this.page.locator(`tr:has-text("${scheduleName}")`);
    await scheduleRow.click();

    // Wait for the delete button to become visible after row selection
    await this.deleteButton.waitFor({ state: 'visible', timeout: 5000 });
  }

  async deleteSelectedSchedule(): Promise<void> {
    // Ensure delete button is visible and clickable
    await this.deleteButton.waitFor({ state: 'visible', timeout: 5000 });
    await this.deleteButton.click();

    // Wait for the schedule to be removed from the table
    await this.page.waitForTimeout(1000);
  }

  getScheduleByName(scheduleName: string): Locator {
    return this.page.locator(`tr:has-text("${scheduleName}")`);
  }

  async waitForSchedulesToLoad(): Promise<void> {
    // Wait for either table to appear or no data message
    await this.page.waitForFunction(
      () => {
        const table = document.querySelector('.schedules-table');
        const noData = document.querySelector('.c-no-data');
        const loading = document.querySelector('.q-inner-loading');

        return (table && table.querySelectorAll('tbody tr').length > 0) || noData || !loading;
      },
      { timeout: 10000 }
    );
  }

  async getScheduleCount(): Promise<number> {
    await this.schedulesTable.scrollIntoViewIfNeeded();
    await this.page.waitForSelector('.q-virtual-scroll__content tr.cursor-pointer', { timeout: 5000 });
    const count = await this.schedulesTableRows.count();
    return count;
  }
}

<testsuites id="" name="" tests="2" failures="2" skipped="0" errors="0" time="352.83049100000005">
<testsuite name="local-syndication-tests\welcomePage\settingsPage\formatsTab\assingCollections.spec.ts" timestamp="2025-08-07T08:15:20.430Z" hostname="chromium-local" tests="2" failures="2" skipped="0" time="240.299" errors="0">
<testcase name="Assign collections › Add and remove a channel collection to API format" classname="local-syndication-tests\welcomePage\settingsPage\formatsTab\assingCollections.spec.ts" time="120.157">
<failure message="assingCollections.spec.ts:34:7 Add and remove a channel collection to API format" type="FAILURE">
<![CDATA[  [chromium-local] › local-syndication-tests\welcomePage\settingsPage\formatsTab\assingCollections.spec.ts:34:7 › Assign collections › Add and remove a channel collection to API format 

    Test timeout of 120000ms exceeded while running "beforeEach" hook.

      24 |   });
      25 |
    > 26 |   test.beforeEach(async ({ localPage, envConfig }) => {
         |        ^
      27 |     await localPage.goto(envConfig.Url);
      28 |     await welcomeToSyndicatePlusPage.settingsButton.click();
      29 |     await expect.soft(welcomeSettingsPage.formatsTab, 'FormatsTab is not visible').toBeVisible();

        at C:\dev\iPMC\syndicate-plus\test\tests\local-syndication-tests\welcomePage\settingsPage\formatsTab\assingCollections.spec.ts:26:8

    Error: locator.click: Target page, context or browser has been closed
    Call log:
      - waiting for getByLabel('settings')


      26 |   test.beforeEach(async ({ localPage, envConfig }) => {
      27 |     await localPage.goto(envConfig.Url);
    > 28 |     await welcomeToSyndicatePlusPage.settingsButton.click();
         |                                                     ^
      29 |     await expect.soft(welcomeSettingsPage.formatsTab, 'FormatsTab is not visible').toBeVisible();
      30 |     await welcomeSettingsPage.formatsTab.click();
      31 |     await expect(formatTabPage.formatsTable, 'Formats Table is not visible').toBeVisible();

        at C:\dev\iPMC\syndicate-plus\test\tests\local-syndication-tests\welcomePage\settingsPage\formatsTab\assingCollections.spec.ts:28:53

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\local-syndication-tests-we-af8ed-el-collection-to-API-format-chromium-local\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|local-syndication-tests-we-af8ed-el-collection-to-API-format-chromium-local\test-failed-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Assign collections › Add and remove a workarea collection to format file" classname="local-syndication-tests\welcomePage\settingsPage\formatsTab\assingCollections.spec.ts" time="120.142">
<failure message="assingCollections.spec.ts:61:7 Add and remove a workarea collection to format file" type="FAILURE">
<![CDATA[  [chromium-local] › local-syndication-tests\welcomePage\settingsPage\formatsTab\assingCollections.spec.ts:61:7 › Assign collections › Add and remove a workarea collection to format file 

    Test timeout of 120000ms exceeded while running "beforeEach" hook.

      24 |   });
      25 |
    > 26 |   test.beforeEach(async ({ localPage, envConfig }) => {
         |        ^
      27 |     await localPage.goto(envConfig.Url);
      28 |     await welcomeToSyndicatePlusPage.settingsButton.click();
      29 |     await expect.soft(welcomeSettingsPage.formatsTab, 'FormatsTab is not visible').toBeVisible();

        at C:\dev\iPMC\syndicate-plus\test\tests\local-syndication-tests\welcomePage\settingsPage\formatsTab\assingCollections.spec.ts:26:8

    Error: locator.click: Target page, context or browser has been closed
    Call log:
      - waiting for getByLabel('settings')


      26 |   test.beforeEach(async ({ localPage, envConfig }) => {
      27 |     await localPage.goto(envConfig.Url);
    > 28 |     await welcomeToSyndicatePlusPage.settingsButton.click();
         |                                                     ^
      29 |     await expect.soft(welcomeSettingsPage.formatsTab, 'FormatsTab is not visible').toBeVisible();
      30 |     await welcomeSettingsPage.formatsTab.click();
      31 |     await expect(formatTabPage.formatsTable, 'Formats Table is not visible').toBeVisible();

        at C:\dev\iPMC\syndicate-plus\test\tests\local-syndication-tests\welcomePage\settingsPage\formatsTab\assingCollections.spec.ts:28:53

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\local-syndication-tests-we-eb241-a-collection-to-format-file-chromium-local\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|local-syndication-tests-we-eb241-a-collection-to-format-file-chromium-local\test-failed-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>
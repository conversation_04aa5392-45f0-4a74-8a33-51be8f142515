import { test, expect } from '@fixtures/localPageFixture';
import { WelcomeToSyndicatePlusPage } from '@pages/welcomePage/welcomeToSyndicatePlus.page';
import { CorePage } from '@pages/core/core.page';
import { CoreProductsTabPage } from '@pages/core/coreProductsTab.page';
import { SchedulesTabPage } from '@pages/core/schedules/schedulesTab.page';
import { RunSyndicationDialogPage } from '@pages/core/schedules/runSyndicationDialog.page';
import { CoreCollectionsTabPage } from '@pages/core/coreCollectionsTab.page';

test.describe('Core Schedule Syndication Integration Tests', () => {
  const UITestFormat = 'UITest format';
  const UITestNode01Channel01 = 'UI Test - Node 01 (UI Test - Channel 01)';
  const UITestProduct01 = 'UI Test - Product 01 code';
  const UITestCSVOutput = 'UITestCSV (csv)';
  const UITestCSV = 'UITestCSV';
  const UITestXMLOutput = 'UITestXML (xml)';
  const UITestEXCELOutput = 'UITestEXCELproducts (excel)';

  const dailyScheduleName = `Daily Core Schedule ${Date.now()}`;
  const weeklyScheduleName = `Weekly Core Schedule ${Date.now()}`;
  const monthlyScheduleName = `Monthly Core Schedule ${Date.now()}`;
  const onceScheduleName = `Once Core Schedule ${Date.now()}`;

  // Helper function to generate future time
  const getFutureTime = (minutesFromNow = 30): string => {
    const futureTime = new Date();
    futureTime.setMinutes(futureTime.getMinutes() + minutesFromNow);
    const hours = String(futureTime.getHours()).padStart(2, '0');
    const minutes = String(futureTime.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
  };

  let welcomeToSyndicatePlusPage: WelcomeToSyndicatePlusPage;
  let corePage: CorePage;
  let coreProductsTabPage: CoreProductsTabPage;
  let schedulesTabPage: SchedulesTabPage;
  let runSyndicationDialog: RunSyndicationDialogPage;
  let coreCollectionsTabPage: CoreCollectionsTabPage;

  test.beforeAll(async ({ localPage }) => {
    welcomeToSyndicatePlusPage = new WelcomeToSyndicatePlusPage(localPage);
    corePage = new CorePage(localPage);
    coreProductsTabPage = new CoreProductsTabPage(localPage);
    schedulesTabPage = new SchedulesTabPage(localPage);
    runSyndicationDialog = new RunSyndicationDialogPage(localPage);
    coreCollectionsTabPage = new CoreCollectionsTabPage(localPage);
  });

  test.beforeEach(async ({ localPage, envConfig }) => {
    await localPage.goto(envConfig.Url);
    await welcomeToSyndicatePlusPage.openFormat(UITestFormat);
  });

  test.afterEach(async ({ localPage }) => {
    // Skip cleanup if page is closed
    if (localPage.isClosed()) {
      console.warn('Page was closed, skipping cleanup');
      return;
    }
  });

  test.describe('Schedule Creation from Products Tab', () => {
    test('Create daily schedule for products syndication @notForPr', async () => {
      await corePage.clickProductsTab();
      await coreProductsTabPage.selectChannel(UITestNode01Channel01);
      await coreProductsTabPage.selectProduct(UITestProduct01);
      await coreProductsTabPage.syndicateClick();
      await runSyndicationDialog.waitForDialog();
      await expect(runSyndicationDialog.dialog).toBeVisible();
      await expect(runSyndicationDialog.outputSelect).toBeVisible();
      await runSyndicationDialog.selectOutput(UITestCSVOutput);
      await expect(runSyndicationDialog.scheduleCheckbox).toBeVisible();
      await runSyndicationDialog.enableSchedule();
      await expect(runSyndicationDialog.scheduleCheckbox).toBeChecked();
      await expect(runSyndicationDialog.scheduledRunConfiguration.scheduleNameInput).toBeVisible();
      await runSyndicationDialog.scheduledRunConfiguration.fillScheduleName(dailyScheduleName);
      await runSyndicationDialog.scheduledRunConfiguration.selectFrequency('daily');
      const timeString = getFutureTime(30);
      await runSyndicationDialog.scheduledRunConfiguration.setTime(timeString);
      await runSyndicationDialog.confirm();
      await runSyndicationDialog.waitForDialogToClose();

      // Verify schedule appears in schedules tab
      await corePage.clickSchedulesTab();
      await schedulesTabPage.waitForSchedulesToLoad();
      await expect(schedulesTabPage.getScheduleByName(dailyScheduleName)).toBeVisible();

      // Delete the created schedule
      await schedulesTabPage.selectSchedule(dailyScheduleName);
      await schedulesTabPage.deleteSelectedSchedule();

      // Verify schedule is removed
      await expect(schedulesTabPage.getScheduleByName(dailyScheduleName)).not.toBeVisible();
    });

    test('Create weekly schedule with multiple days @notForPr', async () => {
      await corePage.clickProductsTab();
      await coreProductsTabPage.selectChannel(UITestNode01Channel01);
      await coreProductsTabPage.selectProduct(UITestProduct01);
      await coreProductsTabPage.syndicateClick();
      await runSyndicationDialog.waitForDialog();
      await expect(runSyndicationDialog.dialog).toBeVisible();
      await expect(runSyndicationDialog.outputSelect).toBeVisible();
      await runSyndicationDialog.selectOutput(UITestCSVOutput);
      await expect(runSyndicationDialog.scheduleCheckbox).toBeVisible();
      await runSyndicationDialog.enableSchedule();
      await expect(runSyndicationDialog.scheduleCheckbox).toBeChecked();

      // Create weekly schedule
      await runSyndicationDialog.createScheduledSyndication(weeklyScheduleName, 'weekly', {
        days: ['monday', 'wednesday', 'friday'],
        time: getFutureTime(45),
        outputName: UITestXMLOutput,
      });

      // Wait for dialog to close
      await runSyndicationDialog.waitForDialogToClose();

      // Verify schedule appears in schedules tab
      await corePage.clickSchedulesTab();
      await schedulesTabPage.waitForSchedulesToLoad();
      await expect(schedulesTabPage.getScheduleByName(weeklyScheduleName)).toBeVisible();

      // Delete the created schedule to clean up
      await schedulesTabPage.selectSchedule(weeklyScheduleName);
      await schedulesTabPage.deleteSelectedSchedule();

      // Verify schedule is removed
      await expect(schedulesTabPage.getScheduleByName(weeklyScheduleName)).not.toBeVisible();
    });

    test('Create monthly schedule with future date/time @notForPr', async () => {
      await corePage.clickProductsTab();
      await coreProductsTabPage.selectChannel(UITestNode01Channel01);
      await coreProductsTabPage.selectProduct(UITestProduct01);
      await coreProductsTabPage.syndicateClick();
      await runSyndicationDialog.waitForDialog();
      await expect(runSyndicationDialog.dialog).toBeVisible();
      await expect(runSyndicationDialog.outputSelect).toBeVisible();
      await runSyndicationDialog.selectOutput(UITestCSVOutput);
      await expect(runSyndicationDialog.scheduleCheckbox).toBeVisible();
      await runSyndicationDialog.enableSchedule();
      await expect(runSyndicationDialog.scheduleCheckbox).toBeChecked();

      // Create monthly schedule with future date/time
      const futureDateTime = new Date();
      futureDateTime.setMonth(futureDateTime.getMonth() + 1);
      const year = futureDateTime.getFullYear();
      const month = String(futureDateTime.getMonth() + 1).padStart(2, '0');
      const day = String(futureDateTime.getDate()).padStart(2, '0');
      const hour = String(futureDateTime.getHours()).padStart(2, '0');
      const minute = String(futureDateTime.getMinutes()).padStart(2, '0');
      const formattedDateTime = `${year}/${month}/${day} ${hour}:${minute}`;

      await runSyndicationDialog.createScheduledSyndication(monthlyScheduleName, 'monthly', {
        dateTime: formattedDateTime,
        outputName: UITestEXCELOutput,
      });

      // Wait for dialog to close
      await runSyndicationDialog.waitForDialogToClose();

      // Verify schedule appears in schedules tab
      await corePage.clickSchedulesTab();
      await schedulesTabPage.waitForSchedulesToLoad();
      await expect(schedulesTabPage.getScheduleByName(monthlyScheduleName)).toBeVisible();

      // Delete the created schedule to clean up
      await schedulesTabPage.selectSchedule(monthlyScheduleName);
      await schedulesTabPage.deleteSelectedSchedule();

      // Verify schedule is removed
      await expect(schedulesTabPage.getScheduleByName(monthlyScheduleName)).not.toBeVisible();
    });

    test('Create once schedule with specific execution time @notForPr', async () => {
      // Navigate to products tab
      await corePage.clickProductsTab();
      await coreProductsTabPage.selectChannel(UITestNode01Channel01);

      // Select a product to enable syndication buttons
      await coreProductsTabPage.selectProduct(UITestProduct01);

      // Open syndication dialog (hover and click)
      await coreProductsTabPage.syndicateClick();
      await runSyndicationDialog.waitForDialog();

      // Select output
      await runSyndicationDialog.selectOutput(UITestCSVOutput);

      // Click scheduled checkbox to enable scheduling options
      await runSyndicationDialog.enableSchedule();

      // Create once schedule with future date/time
      const futureDateTime = new Date();
      futureDateTime.setDate(futureDateTime.getDate() + 7); // One week from now
      const year = futureDateTime.getFullYear();
      const month = String(futureDateTime.getMonth() + 1).padStart(2, '0');
      const day = String(futureDateTime.getDate()).padStart(2, '0');
      const hour = String(futureDateTime.getHours()).padStart(2, '0');
      const minute = String(futureDateTime.getMinutes()).padStart(2, '0');
      const formattedDateTime = `${year}/${month}/${day} ${hour}:${minute}`;

      await runSyndicationDialog.createScheduledSyndication(onceScheduleName, 'once', {
        dateTime: formattedDateTime,
        outputName: UITestCSVOutput,
      });

      // Wait for dialog to close
      await runSyndicationDialog.waitForDialogToClose();

      // Verify schedule appears in schedules tab
      await corePage.clickSchedulesTab();
      await schedulesTabPage.waitForSchedulesToLoad();
      await expect(schedulesTabPage.getScheduleByName(onceScheduleName)).toBeVisible();

      // Delete the created schedule to clean up
      await schedulesTabPage.selectSchedule(onceScheduleName);
      await schedulesTabPage.deleteSelectedSchedule();

      // Verify schedule is removed
      await expect(schedulesTabPage.getScheduleByName(onceScheduleName)).not.toBeVisible();
    });
  });

  test.describe('Schedule Creation from Collections Tab', () => {
    test('Create schedule for collection syndication @notForPr', async () => {
      // Navigate to collections tab
      await corePage.collectionsTab.click();
      await expect(coreCollectionsTabPage.collectionTable).toBeVisible();

      // Select a collection and syndicate
      await coreCollectionsTabPage.selectCollection(UITestNode01Channel01).click();
      await coreCollectionsTabPage.syndicateClick();
      await runSyndicationDialog.waitForDialog();

      // Select output
      await runSyndicationDialog.selectOutput(UITestCSVOutput);

      // Click scheduled checkbox to enable scheduling options
      await runSyndicationDialog.enableSchedule();

      // Create daily schedule for collection
      await runSyndicationDialog.createScheduledSyndication(`${dailyScheduleName} - Collection`, 'daily', {
        time: getFutureTime(60),
        outputName: UITestCSVOutput,
      });

      // Wait for dialog to close
      await runSyndicationDialog.waitForDialogToClose();

      // Verify schedule appears in schedules tab
      await corePage.clickSchedulesTab();
      await schedulesTabPage.waitForSchedulesToLoad();
      await expect(schedulesTabPage.getScheduleByName(`${dailyScheduleName} - Collection`)).toBeVisible();

      // Delete the created schedule to clean up
      await schedulesTabPage.selectSchedule(`${dailyScheduleName} - Collection`);
      await schedulesTabPage.deleteSelectedSchedule();

      // Verify schedule is removed
      await expect(schedulesTabPage.getScheduleByName(`${dailyScheduleName} - Collection`)).not.toBeVisible();
    });
  });

  test.describe('Schedule Dialog Validation and UI Behavior', () => {
    test.beforeEach(async () => {
      // Open syndication dialog
      await corePage.clickProductsTab();
      await coreProductsTabPage.selectChannel(UITestNode01Channel01);
      await coreProductsTabPage.selectProduct(UITestProduct01);
      await coreProductsTabPage.syndicateClick();
      await runSyndicationDialog.waitForDialog();
    });

    test('Scheduled checkbox controls schedule configuration visibility @notForPr', async () => {
      // Initially, scheduled checkbox should be unchecked and configuration hidden
      await expect(runSyndicationDialog.scheduleCheckbox).not.toBeChecked();
      await expect(runSyndicationDialog.scheduledRunConfiguration.scheduleNameInput).not.toBeVisible();

      // Click scheduled checkbox to enable scheduling options
      await runSyndicationDialog.enableSchedule();

      // Checkbox should now be checked and configuration visible
      await expect(runSyndicationDialog.scheduleCheckbox).toBeChecked();
      await expect(runSyndicationDialog.scheduledRunConfiguration.scheduleNameInput).toBeVisible();

      // Click checkbox again to disable scheduling
      await runSyndicationDialog.disableSchedule();

      // Checkbox should be unchecked and configuration hidden again
      await expect(runSyndicationDialog.scheduleCheckbox).not.toBeChecked();
      await expect(runSyndicationDialog.scheduledRunConfiguration.scheduleNameInput).not.toBeVisible();
    });

    test('Schedule configuration shows/hides correctly @notForPr', async () => {
      // Initially configuration should not be visible
      await expect(runSyndicationDialog.scheduledRunConfiguration.scheduleNameInput).not.toBeVisible();

      // Click scheduled checkbox to enable scheduling options
      await runSyndicationDialog.enableSchedule();

      // Configuration should now be visible
      await expect(runSyndicationDialog.scheduledRunConfiguration.scheduleNameInput).toBeVisible();
      await expect(runSyndicationDialog.scheduledRunConfiguration.dailyButton).toBeVisible();
      await expect(runSyndicationDialog.scheduledRunConfiguration.weeklyButton).toBeVisible();
      await expect(runSyndicationDialog.scheduledRunConfiguration.monthlyButton).toBeVisible();
      await expect(runSyndicationDialog.scheduledRunConfiguration.onceButton).toBeVisible();
    });

    test('Days selection appears for weekly frequency @notForPr', async () => {
      await runSyndicationDialog.enableSchedule();
      await runSyndicationDialog.scheduledRunConfiguration.selectFrequency('weekly');
      await expect(runSyndicationDialog.scheduledRunConfiguration.daysContainer).toBeVisible();
      await expect(runSyndicationDialog.scheduledRunConfiguration.mondayButton).toBeVisible();
      await expect(runSyndicationDialog.scheduledRunConfiguration.tuesdayButton).toBeVisible();
      await expect(runSyndicationDialog.scheduledRunConfiguration.wednesdayButton).toBeVisible();
      await expect(runSyndicationDialog.scheduledRunConfiguration.thursdayButton).toBeVisible();
      await expect(runSyndicationDialog.scheduledRunConfiguration.fridayButton).toBeVisible();
      await expect(runSyndicationDialog.scheduledRunConfiguration.saturdayButton).toBeVisible();
      await expect(runSyndicationDialog.scheduledRunConfiguration.sundayButton).toBeVisible();
    });

    test('Date/time picker appears for appropriate frequencies @notForPr', async () => {
      await runSyndicationDialog.enableSchedule();
      await runSyndicationDialog.scheduledRunConfiguration.selectFrequency('once');
      await expect(runSyndicationDialog.scheduledRunConfiguration.dateTimeInput).toBeVisible();
      await expect(runSyndicationDialog.scheduledRunConfiguration.calendarIcon).toBeVisible();
      await runSyndicationDialog.scheduledRunConfiguration.selectFrequency('monthly');
      await expect(runSyndicationDialog.scheduledRunConfiguration.dateTimeInput).toBeVisible();
    });

    test('Time-only input appears for daily frequency @notForPr', async () => {
      await runSyndicationDialog.enableSchedule();
      await runSyndicationDialog.scheduledRunConfiguration.selectFrequency('daily');
      await expect(runSyndicationDialog.scheduledRunConfiguration.timeInput).toBeVisible();
      await expect(runSyndicationDialog.scheduledRunConfiguration.daysContainer).not.toBeVisible();
    });

    test('Dialog validation works correctly @notForPr', async () => {
      await runSyndicationDialog.selectOutput(UITestCSVOutput);
      await runSyndicationDialog.enableSchedule();
      await expect(runSyndicationDialog.confirmButton).toBeDisabled();
      await runSyndicationDialog.scheduledRunConfiguration.fillScheduleName('Test Schedule Validation');
      await runSyndicationDialog.scheduledRunConfiguration.selectFrequency('daily');
      await runSyndicationDialog.scheduledRunConfiguration.setTime('14:30');
      await expect(runSyndicationDialog.confirmButton).toBeEnabled();
    });
  });

  test.describe('Cross-Tab Schedule Integration', () => {
    test('Schedule workflow across different tabs @notForPr', async () => {
      const crossTabScheduleName = `Cross Tab Core Schedule ${Date.now()}`;

      // Step 1: Create schedule from products tab
      await corePage.clickProductsTab();
      await coreProductsTabPage.selectChannel(UITestNode01Channel01);
      await coreProductsTabPage.selectProduct(UITestProduct01);
      await coreProductsTabPage.syndicateClick();
      await runSyndicationDialog.waitForDialog();

      // Select output and enable scheduling
      await runSyndicationDialog.selectOutput(UITestCSVOutput);
      await runSyndicationDialog.enableSchedule();

      await runSyndicationDialog.createScheduledSyndication(crossTabScheduleName, 'daily', {
        time: getFutureTime(90),
        outputName: UITestCSVOutput,
      });

      await runSyndicationDialog.waitForDialogToClose();

      // Step 2: Verify in schedules tab
      await corePage.clickSchedulesTab();
      await schedulesTabPage.waitForSchedulesToLoad();
      await expect(schedulesTabPage.getScheduleByName(crossTabScheduleName)).toBeVisible();

      // Step 3: Verify the schedule details contain expected information
      const scheduleRow = schedulesTabPage.getScheduleByName(crossTabScheduleName);
      await expect(scheduleRow).toContainText(crossTabScheduleName);
      await expect(scheduleRow).toContainText(UITestCSV);

      // Step 4: Delete from schedules tab
      await schedulesTabPage.selectSchedule(crossTabScheduleName);
      await schedulesTabPage.deleteSelectedSchedule();

      // Step 5: Verify removal
      await expect(schedulesTabPage.getScheduleByName(crossTabScheduleName)).not.toBeVisible();
    });
  });

  test.describe('Schedule Tab Empty State', () => {
    test('Show empty state when no schedules exist @notForPr', async () => {
      await corePage.clickSchedulesTab();
      // Wait for either the empty state or a schedule row

      await Promise.race([
        schedulesTabPage.noDataContainer.waitFor({ state: 'visible', timeout: 10000 }),
        schedulesTabPage.schedulesTableRows
          .first()
          .waitFor({ state: 'visible', timeout: 10000 })
          .catch(() => {
            /* ignore if not visible */
          }),
      ]);

      // Delete all schedules if any exist
      while (
        await schedulesTabPage.schedulesTableRows
          .first()
          .isVisible()
          .catch(() => false)
      ) {
        await schedulesTabPage.schedulesTableRows.first().click();
        await schedulesTabPage.deleteSelectedSchedule();
        // Wait for either the next row or the empty state
        await Promise.race([
          schedulesTabPage.noDataContainer.waitFor({ state: 'visible', timeout: 10000 }),
          schedulesTabPage.schedulesTableRows
            .first()
            .waitFor({ state: 'visible', timeout: 10000 })
            .catch(() => {
              /* ignore if not visible */
            }),
        ]);
      }

      // Assert empty state
      await expect(schedulesTabPage.noDataContainer).toBeVisible();
      await expect(schedulesTabPage.noDataTitle).toContainText('no schedules yet');
      await expect(schedulesTabPage.noDataMessage).toContainText('you have no scheduled syndications yet');
    });
  });
});
